# 🔴 THE OLD WAY: String-Based Prompting (What NOT to do)
import openai

def traditional_approach_problems():
    """
    🚨 CRITICAL INSIGHT: This approach breaks down quickly!
    Problems:
    - Hardcoded strings are brittle
    - No systematic optimization
    - Difficult to debug when things go wrong
    - Doesn't scale to complex applications
    """
    
    # ❌ BAD: Hardcoded prompt strings
    prompt_template = """
    You are a helpful assistant. Please analyze this document: {document}
    
    Provide a summary that includes:
    - Main points
    - Key findings
    - Recommendations
    
    Format your response as JSON.
    """
    
    # ❌ BAD: Manual prompt engineering through trial and error
    def analyze_document(document_text):
        # What happens when this doesn't work? You're stuck!
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt_template.format(document=document_text)}]
        )
        
        # ❌ BAD: Hope and pray the format is correct
        try:
            return response.choices[0].message.content
        except:
            return "Something went wrong 🤷‍♂️"

# 🟢 THE NEW WAY: DSPy Programming (What you SHOULD do)
import dspy

class ModernApproach:
    """
    ✨ BREAKTHROUGH MOMENT: Programming with AI, not prompting!
    Benefits:
    - Systematic and reproducible
    - Automatically optimizes for your specific use case
    - Easy to debug and improve
    - Scales to complex multi-step applications
    """
    
    def __init__(self):
        # 🎯 PRO TIP: Configuration is separate from logic
        dspy.settings.configure(
            lm=dspy.OpenAI(model="gpt-4"),
            rm=dspy.ColBERTv2()  # Retrieval model for context
        )
        
        # ✅ GOOD: Declarative signatures define what you want
        self.document_analyzer = dspy.ChainOfThought(DocumentAnalysisSignature)
    
    def analyze_document(self, document_text):
        """
        🔧 ATOMIC INSIGHT: DSPy handles the complex prompt construction
        You focus on WHAT you want, not HOW to ask for it
        """
        # The magic happens here - DSPy optimizes the actual prompts
        result = self.document_analyzer(document=document_text)
        
        return {
            "summary": result.summary,
            "key_findings": result.key_findings,
            "recommendations": result.recommendations,
            "confidence": result.confidence_score
        }

class DocumentAnalysisSignature(dspy.Signature):
    """
    🚀 CRITICAL CONCEPT: Signatures define the interface, not the implementation
    This is like a function signature in programming - it specifies inputs and outputs
    """
    document = dspy.InputField(desc="The document text to analyze")
    summary = dspy.OutputField(desc="A concise summary of the main points")
    key_findings = dspy.OutputField(desc="Important discoveries or insights")
    recommendations = dspy.OutputField(desc="Actionable next steps")
    confidence_score = dspy.OutputField(desc="Confidence in the analysis (0-1)")

# 🎓 TEACHING MOMENT: Let's see the difference in action!
def demonstrate_paradigm_shift():
    """
    This function shows why DSPy is revolutionary:
    1. More reliable outputs
    2. Easier to modify and improve
    3. Systematic optimization capabilities
    4. Better error handling and debugging
    """
    
    # Traditional approach - fragile and hard to improve
    traditional_result = traditional_approach_problems()
    print(f"Traditional: {traditional_result}")
    
    # DSPy approach - robust and optimizable
    modern_analyzer = ModernApproach()
    dspy_result = modern_analyzer.analyze_document("Sample document text...")
    print(f"DSPy: {dspy_result}")
    
    # 💡 KEY INSIGHT: DSPy result is structured, reliable, and improvable!

# 🔍 ENGAGEMENT CHECK: Can you spot the differences?
"""
REFLECTION QUESTIONS for Students:
1. What happens when the traditional approach fails?
2. How would you improve the traditional approach?
3. What makes the DSPy approach more maintainable?
4. Which approach would you rather debug when something goes wrong?
"""