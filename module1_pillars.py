# 🏛️ THE THREE PILLARS OF DSPY ARCHITECTURE
"""
🎓 FOUNDATIONAL INSIGHT: DSPy has three core components that work together:

1. 📝 SIGNATURES: Define the interface (what goes in, what comes out)
2. 🧩 MODULES: Implement the logic (how the transformation happens)  
3. ⚙️ OPTIMIZERS: Improve performance (automatic tuning and optimization)

Think of it like building a house:
- Signatures = Blueprint (what rooms, where doors go)
- Modules = Construction process (how to build each room)
- Optimizers = Quality control (making sure everything works perfectly)
"""

import dspy
from typing import List, Optional

# 🏗️ PILLAR 1: SIGNATURES - The Blueprint
class BasicSignature(dspy.Signature):
    """
    🔍 CRITICAL CONCEPT: Signatures are like function definitions
    They tell DSPy what transformation you want, not how to do it
    
    Compare to regular Python:
    def add_numbers(a: int, b: int) -> int:  # This is a signature
        return a + b                         # This is implementation
    
    In DSPy:
    class Addition(dspy.Signature):          # This is a signature
        a = dspy.InputField(desc="First number")
        b = dspy.InputField(desc="Second number")  
        result = dspy.OutputField(desc="Sum of a and b")
    """
    
    question = dspy.InputField(desc="A question that needs answering")
    context = dspy.InputField(desc="Background information relevant to the question")
    answer = dspy.OutputField(desc="A helpful, accurate answer")
    confidence = dspy.OutputField(desc="How confident you are (0-100)")

# 💡 TEACHING MOMENT: Signature Evolution
class BeginnerSignature(dspy.Signature):
    """🟢 BEGINNER LEVEL: Simple input-output"""
    question = dspy.InputField()
    answer = dspy.OutputField()

class IntermediateSignature(dspy.Signature):
    """🟡 INTERMEDIATE LEVEL: Multiple inputs/outputs with descriptions"""
    question = dspy.InputField(desc="User's question")
    context = dspy.InputField(desc="Relevant background information")
    answer = dspy.OutputField(desc="Comprehensive answer")
    sources = dspy.OutputField(desc="List of information sources used")

class AdvancedSignature(dspy.Signature):
    """🔴 ADVANCED LEVEL: Complex reasoning with metadata"""
    question = dspy.InputField(desc="User's question requiring multi-step reasoning")
    context = dspy.InputField(desc="Retrieved context from knowledge base")
    reasoning_steps = dspy.OutputField(desc="Step-by-step thought process")
    answer = dspy.OutputField(desc="Final synthesized answer")
    confidence = dspy.OutputField(desc="Confidence score with justification")
    follow_up_questions = dspy.OutputField(desc="Suggested related questions")

# 🏗️ PILLAR 2: MODULES - The Construction Process
class BasicModule(dspy.Module):
    """
    🔧 ATOMIC INSIGHT: Modules are like smart functions that use AI
    They take signatures and make them work with actual language models
    
    Think of modules as:
    - Signature = Recipe ingredients and final dish
    - Module = The actual cooking process and techniques
    """
    
    def __init__(self):
        super().__init__()
        # 🎯 PRO TIP: Different module types for different reasoning patterns
        self.simple_qa = dspy.Predict(BasicSignature)
        self.reasoning_qa = dspy.ChainOfThought(BasicSignature)
        self.multi_attempt = dspy.Retry(BasicSignature)
    
    def forward(self, question: str, context: str) -> dspy.Prediction:
        """
        🚀 CRITICAL CONCEPT: forward() is where the magic happens
        This is like the main() function of your AI program
        """
        # Choose reasoning strategy based on question complexity
        if self._is_complex_question(question):
            result = self.reasoning_qa(question=question, context=context)
        else:
            result = self.simple_qa(question=question, context=context)
        
        return result
    
    def _is_complex_question(self, question: str) -> bool:
        """Helper method to determine reasoning complexity needed"""
        complex_indicators = ["why", "how", "analyze", "compare", "evaluate"]
        return any(indicator in question.lower() for indicator in complex_indicators)

# 🧩 MODULE TYPES SHOWCASE
class ModuleTypesShowcase:
    """
    🎓 LEARNING OBJECTIVE: Understand when to use different module types
    Each module type implements different reasoning patterns
    """
    
    def __init__(self):
        # 🔸 PREDICT: Direct input→output transformation
        self.predictor = dspy.Predict("question -> answer")
        
        # 🔸 CHAIN OF THOUGHT: Step-by-step reasoning
        self.reasoner = dspy.ChainOfThought("question -> reasoning, answer")
        
        # 🔸 RETRY: Multiple attempts with error correction
        self.retry_predictor = dspy.Retry(BasicSignature)
        
        # 🔸 MAJORITY VOTE: Multiple predictions, best answer wins
        self.ensemble = dspy.Majority(BasicSignature)
    
    def demonstrate_module_differences(self, question: str):
        """
        🔍 ENGAGEMENT ACTIVITY: See how different modules handle the same question
        This helps students understand when to use each approach
        """
        
        results = {}
        
        # Simple prediction
        results['predict'] = self.predictor(question=question)
        
        # Reasoning-based prediction  
        results['chain_of_thought'] = self.reasoner(question=question)
        
        # Multiple attempts (good for difficult questions)
        results['retry'] = self.retry_predictor(question=question)
        
        # Ensemble approach (most reliable)
        results['majority'] = self.ensemble(question=question)
        
        return results

# 🏗️ PILLAR 3: OPTIMIZERS - The Quality Control System
class OptimizerShowcase:
    """
    ⚙️ REVOLUTIONARY CONCEPT: Optimizers automatically improve your prompts!
    This is what makes DSPy fundamentally different from manual prompt engineering
    
    🔍 KEY INSIGHT: You write the logic, DSPy figures out the best prompts
    """
    
    def __init__(self):
        self.basic_module = BasicModule()
    
    def demonstrate_optimization(self, training_examples: List[dspy.Example]):
        """
        🚀 BREAKTHROUGH MOMENT: Watch DSPy improve itself!
        
        The optimizer will:
        1. Analyze your training examples
        2. Try different prompt variations  
        3. Find the best-performing prompts
        4. Update your module automatically
        """
        
        # 🔧 BOOTSTRAP OPTIMIZER: Generates better examples
        bootstrap_optimizer = dspy.BootstrapFewShot(
            metric=self._accuracy_metric,
            max_examples=5
        )
        
        # 🎯 OPTIMIZATION PROCESS
        print("🔄 Starting optimization process...")
        optimized_module = bootstrap_optimizer.compile(
            self.basic_module, 
            trainset=training_examples
        )
        
        print("✅ Optimization complete!")
        return optimized_module
    
    def _accuracy_metric(self, example, prediction, trace=None):
        """
        📊 PERFORMANCE INSIGHT: Define how to measure success
        The optimizer uses this to know what "better" means
        """
        # Simple accuracy check (you can make this more sophisticated)
        return example.answer.lower() in prediction.answer.lower()

# 🎓 HANDS-ON LEARNING EXERCISE
class FirstDSPyProgram:
    """
    🛠️ PRACTICAL APPLICATION: Your first complete DSPy program
    This brings together all three pillars in a working example
    """
    
    def __init__(self):
        # Configure DSPy (you only do this once)
        dspy.settings.configure(
            lm=dspy.OpenAI(model="gpt-3.5-turbo"),  # Language model
            # rm=dspy.ColBERTv2()  # Retrieval model (optional)
        )
        
        # Create your intelligent module
        self.qa_system = BasicModule()
    
    def answer_question(self, question: str, context: str = ""):
        """
        🎯 YOUR FIRST AI FUNCTION: Simple but powerful!
        """
        result = self.qa_system(question=question, context=context)
        
        return {
            "answer": result.answer,
            "confidence": result.confidence,
            "reasoning": getattr(result, 'reasoning', 'Direct prediction used')
        }

# 🔍 ENGAGEMENT CHECK: Understanding Verification
def concept_check_questions():
    """
    📝 COMPREHENSION CHECKPOINT: Test your understanding
    
    Answer these questions to verify you understand the core concepts:
    """
    
    questions = [
        {
            "question": "What's the difference between a Signature and a Module?",
            "hint": "Think about blueprint vs. construction process",
            "difficulty": "beginner"
        },
        {
            "question": "When would you use ChainOfThought vs. Predict?",
            "hint": "Consider question complexity and reasoning needs",
            "difficulty": "intermediate"
        },
        {
            "question": "How do optimizers change the way we think about AI programming?",
            "hint": "Compare to manual prompt engineering approaches",
            "difficulty": "advanced"
        }
    ]
    
    return questions

# 🎯 PRACTICAL CHALLENGE: Build Your First System
def student_challenge():
    """
    🏆 CHALLENGE: Create a simple movie recommendation system
    
    Requirements:
    1. Create a signature for movie recommendations
    2. Build a module that uses the signature
    3. Test it with different user preferences
    
    This exercise reinforces all three pillars!
    """
    
    # TODO: Student implementation goes here
    # Hint: Start with MovieRecommendationSignature
    pass