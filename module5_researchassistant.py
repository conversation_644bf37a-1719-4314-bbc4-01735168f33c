import dspy
from typing import List, Dict, Any
import json

# 🎯 CORE RESEARCH ASSISTANT ARCHITECTURE
class ResearchAssistantAgent(dspy.Module):
    """
    🧠 Personal AI Research Assistant with Advanced Reasoning
    
    Features:
    - Intelligent research question analysis
    - Multiple reasoning strategies (CoT, ToT, Meta)
    - Collaborative multi-perspective analysis
    - Self-improving research methodology
    
    Perfect for: Academic research, business analysis, creative projects
    """
    
    def __init__(self):
        super().__init__()
        
        # 🔍 Research Question Analyzer
        self.question_analyzer = dspy.ChainOfThought(
            """research_question -> 
               research_type, complexity_level, required_reasoning_approach, 
               key_subtopics, potential_challenges"""
        )
        
        # 🔗 Chain-of-Thought Researcher (for systematic analysis)
        self.systematic_researcher = dspy.ChainOfThought(
            """research_question, subtopics -> 
               step1_initial_analysis, step2_evidence_gathering, 
               step3_pattern_identification, step4_synthesis, 
               final_findings, confidence_level"""
        )
        
        # 🌳 Tree-of-Thought Explorer (for creative research)
        self.creative_researcher = dspy.ChainOfThought(
            """research_question -> 
               approach1_traditional, approach2_innovative, approach3_interdisciplinary,
               evaluation_criteria, best_approach_reasoning, recommended_methodology"""
        )
        
        # 🤝 Multi-Perspective Analyzer
        self.perspective_analyzer = dspy.ChainOfThought(
            """research_question, context -> 
               academic_perspective, practical_perspective, critical_perspective,
               perspective_synthesis, balanced_conclusion"""
        )
        
        # 🧠 Meta-Research Strategist
        self.meta_strategist = dspy.ChainOfThought(
            """research_question, available_approaches -> 
               optimal_strategy, reasoning_justification, expected_outcomes,
               potential_limitations, improvement_suggestions"""
        )
        
        # ⚖️ Research Synthesizer
        self.synthesizer = dspy.ChainOfThought(
            """question, systematic_findings, creative_findings, perspectives -> 
               integrated_analysis, key_insights, actionable_recommendations,
               areas_for_further_research, confidence_assessment"""
        )
    
    def research(self, research_question: str, context: str = "general") -> Dict[str, Any]:
        """
        🚀 Complete research pipeline with advanced reasoning
        
        Args:
            research_question: The question to research
            context: Research context (academic, business, personal, etc.)
        
        Returns:
            Comprehensive research analysis with multiple perspectives
        """
        
        print("🔍 Starting Research Analysis...")
        print(f"Question: {research_question}")
        print(f"Context: {context}")
        print("=" * 60)
        
        # Phase 1: Analyze the research question
        analysis = self.question_analyzer(research_question=research_question)
        
        print(f"🎯 Research Analysis:")
        print(f"   Type: {analysis.research_type}")
        print(f"   Complexity: {analysis.complexity_level}")
        print(f"   Recommended Approach: {analysis.required_reasoning_approach}")
        print(f"   Key Subtopics: {analysis.key_subtopics}")
        print()
        
        # Phase 2: Choose optimal research strategy
        strategy = self.meta_strategist(
            research_question=research_question,
            available_approaches=analysis.required_reasoning_approach
        )
        
        print(f"🧠 Meta-Strategy Selected:")
        print(f"   Strategy: {strategy.optimal_strategy}")
        print(f"   Reasoning: {strategy.reasoning_justification}")
        print()
        
        # Phase 3: Apply multiple reasoning approaches
        print("🔗 Systematic Research (Chain-of-Thought)...")
        systematic_results = self.systematic_researcher(
            research_question=research_question,
            subtopics=analysis.key_subtopics
        )
        
        print("🌳 Creative Research (Tree-of-Thought)...")
        creative_results = self.creative_researcher(research_question=research_question)
        
        print("🤝 Multi-Perspective Analysis...")
        perspective_results = self.perspective_analyzer(
            research_question=research_question,
            context=context
        )
        
        # Phase 4: Synthesize all findings
        print("⚖️ Synthesizing Research Findings...")
        final_synthesis = self.synthesizer(
            question=research_question,
            systematic_findings=systematic_results.final_findings,
            creative_findings=creative_results.recommended_methodology,
            perspectives=perspective_results.balanced_conclusion
        )
        
        # Phase 5: Compile comprehensive results
        research_report = {
            'research_question': research_question,
            'context': context,
            'analysis': {
                'type': analysis.research_type,
                'complexity': analysis.complexity_level,
                'subtopics': analysis.key_subtopics,
                'challenges': analysis.potential_challenges
            },
            'strategy': {
                'chosen_approach': strategy.optimal_strategy,
                'justification': strategy.reasoning_justification,
                'expected_outcomes': strategy.expected_outcomes
            },
            'systematic_research': {
                'methodology': 'Chain-of-Thought Analysis',
                'findings': systematic_results.final_findings,
                'confidence': systematic_results.confidence_level,
                'reasoning_trace': {
                    'step1': systematic_results.step1_initial_analysis,
                    'step2': systematic_results.step2_evidence_gathering,
                    'step3': systematic_results.step3_pattern_identification,
                    'step4': systematic_results.step4_synthesis
                }
            },
            'creative_research': {
                'methodology': 'Tree-of-Thought Exploration',
                'approaches': {
                    'traditional': creative_results.approach1_traditional,
                    'innovative': creative_results.approach2_innovative,
                    'interdisciplinary': creative_results.approach3_interdisciplinary
                },
                'recommended_approach': creative_results.best_approach_reasoning
            },
            'perspectives': {
                'academic': perspective_results.academic_perspective,
                'practical': perspective_results.practical_perspective,
                'critical': perspective_results.critical_perspective,
                'synthesis': perspective_results.perspective_synthesis
            },
            'final_synthesis': {
                'integrated_analysis': final_synthesis.integrated_analysis,
                'key_insights': final_synthesis.key_insights,
                'recommendations': final_synthesis.actionable_recommendations,
                'further_research': final_synthesis.areas_for_further_research,
                'confidence': final_synthesis.confidence_assessment
            }
        }
        
        return research_report
    
    def display_research_report(self, report: Dict[str, Any]):
        """
        📊 Beautiful research report display
        """
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE RESEARCH REPORT")
        print("=" * 80)
        
        print(f"\n🎯 RESEARCH QUESTION:")
        print(f"   {report['research_question']}")
        
        print(f"\n📋 RESEARCH ANALYSIS:")
        print(f"   Type: {report['analysis']['type']}")
        print(f"   Complexity: {report['analysis']['complexity']}")
        print(f"   Key Subtopics: {report['analysis']['subtopics']}")
        
        print(f"\n🧠 RESEARCH STRATEGY:")
        print(f"   Chosen Approach: {report['strategy']['chosen_approach']}")
        print(f"   Justification: {report['strategy']['justification']}")
        
        print(f"\n🔗 SYSTEMATIC FINDINGS (Chain-of-Thought):")
        print(f"   {report['systematic_research']['findings']}")
        print(f"   Confidence Level: {report['systematic_research']['confidence']}")
        
        print(f"\n🌳 CREATIVE APPROACHES (Tree-of-Thought):")
        print(f"   Traditional: {report['creative_research']['approaches']['traditional']}")
        print(f"   Innovative: {report['creative_research']['approaches']['innovative']}")
        print(f"   Interdisciplinary: {report['creative_research']['approaches']['interdisciplinary']}")
        
        print(f"\n🤝 MULTIPLE PERSPECTIVES:")
        print(f"   Academic: {report['perspectives']['academic']}")
        print(f"   Practical: {report['perspectives']['practical']}")
        print(f"   Critical: {report['perspectives']['critical']}")
        
        print(f"\n⚖️ FINAL SYNTHESIS:")
        print(f"   Key Insights: {report['final_synthesis']['key_insights']}")
        print(f"   Recommendations: {report['final_synthesis']['recommendations']}")
        print(f"   Further Research: {report['final_synthesis']['further_research']}")
        
        print("\n" + "=" * 80)


# 🎮 INTERACTIVE RESEARCH ASSISTANT DEMO
class InteractiveResearchDemo:
    """
    🎮 Interactive demo to test your research assistant!
    
    Try different types of questions and see how the reasoning adapts:
    - Academic questions (uses systematic CoT)
    - Creative problems (uses ToT exploration)
    - Complex analysis (uses multi-perspective + synthesis)
    """
    
    def __init__(self):
        self.assistant = ResearchAssistantAgent()
    
    def run_demo_questions(self):
        """
        🚀 Test the assistant with various question types
        """
        
        demo_questions = [
            {
                'question': "What are the key factors that make a startup successful in the first two years?",
                'context': "business",
                'expected_reasoning': "Should use ToT for multiple business perspectives"
            },
            {
                'question': "How does climate change affect ocean acidification and marine ecosystems?",
                'context': "academic",
                'expected_reasoning': "Should use CoT for systematic scientific analysis"
            },
            {
                'question': "Should I pursue a computer science degree or learn programming through bootcamps and self-study?",
                'context': "personal",
                'expected_reasoning': "Should use multi-perspective analysis"
            }
        ]
        
        for i, demo in enumerate(demo_questions, 1):
            print(f"\n🎯 DEMO {i}: {demo['expected_reasoning']}")
            print("-" * 60)
            
            # Run the research
            report = self.assistant.research(
                research_question=demo['question'],
                context=demo['context']
            )
            
            # Display results
            self.assistant.display_research_report(report)
            
            print(f"\n💡 Teaching Insight: {demo['expected_reasoning']}")
            input("\n⏸️  Press Enter to continue to next demo...")
    
    def interactive_mode(self):
        """
        🎮 Let users ask their own research questions
        """
        print("\n🎮 INTERACTIVE RESEARCH MODE")
        print("Ask any research question and watch the AI choose the best reasoning strategy!")
        print("Type 'quit' to exit")
        
        while True:
            question = input("\n🤔 What would you like to research? ").strip()
            
            if question.lower() in ['quit', 'exit', 'stop']:
                print("👋 Thanks for exploring AI reasoning!")
                break
            
            if not question:
                continue
            
            context = input("🎯 Research context (academic/business/personal/general): ").strip() or "general"
            
            print("\n🚀 Starting research...")
            report = self.assistant.research(question, context)
            self.assistant.display_research_report(report)
            
            feedback = input("\n💭 Was this helpful? (y/n): ").strip().lower()
            if feedback == 'y':
                print("😊 Great! The AI is learning what works!")
            else:
                print("🤔 Thanks for the feedback! This helps improve the system.")


# 🚀 MAIN EXECUTION SCRIPT
def main():
    """
    🎯 Main demo script - shows complete reasoning architecture in action
    """
    
    print("🎓 DSPy Reasoning Architectures - Personal Research Assistant")
    print("=" * 70)
    print("This demo shows how Chain-of-Thought, Tree-of-Thought, and")
    print("meta-reasoning work together in a real agentic AI application!")
    print("=" * 70)
    
    # Create demo instance
    demo = InteractiveResearchDemo()
    
    # Ask user what they want to do
    print("\n🎮 Choose your exploration mode:")
    print("1. 📚 Run pre-made demo questions (shows different reasoning patterns)")
    print("2. 🎯 Interactive mode (ask your own research questions)")
    print("3. 🚀 Both (comprehensive demo)")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        demo.run_demo_questions()
    elif choice == "2":
        demo.interactive_mode()
    elif choice == "3":
        print("\n🚀 Starting comprehensive demo...")
        demo.run_demo_questions()
        print("\n" + "="*50)
        demo.interactive_mode()
    else:
        print("🤔 Starting interactive mode by default...")
        demo.interactive_mode()

# Configuration helper
def setup_dspy():
    """
    🔧 DSPy configuration helper
    
    Note: You'll need to configure DSPy with your preferred LLM
    This is just a template - replace with your actual configuration
    """
    
    print("🔧 DSPy Configuration Required:")
    print("   - Set up your LLM (OpenAI, Anthropic, local model, etc.)")
    print("   - Configure DSPy settings")
    print("   - Example: dspy.settings.configure(lm=dspy.OpenAI(model='gpt-3.5-turbo'))")
    print()
    
    # Uncomment and modify based on your setup:
    # dspy.settings.configure(lm=dspy.OpenAI(model="gpt-3.5-turbo"))

if __name__ == "__main__":
    setup_dspy()
    main()