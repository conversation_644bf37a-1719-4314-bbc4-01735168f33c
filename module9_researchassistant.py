# ============================================================================
# 🤖 PRACTICAL DSPY AGENTIC RESEARCH ASSISTANT
# Complete Integration: Modules 1-8 + Real LLM + Production Features
# ============================================================================

import dspy
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import requests
import os
from abc import ABC, abstractmethod

# ============================================================================
# CORE DSPY CONFIGURATION & LLM INTEGRATION
# ============================================================================

class LLMConfig:
    """
    🔧 PRODUCTION LLM CONFIGURATION
    
    Integrates multiple LLM providers with DSPy for robust production deployment
    Implements fallback mechanisms and API key management
    """
    
    @staticmethod
    def setup_llm(provider: str = "openai", model: str = "gpt-4o-mini"):
        """Configure DSPy with production LLM settings"""
        
        if provider == "openai":
            # OpenAI Integration
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                print("⚠️ OPENAI_API_KEY not found. Using demo mode.")
                return None
            
            lm = dspy.LM(f"openai/{model}", api_key=api_key)
            
        elif provider == "anthropic":
            # Anthropic Claude Integration
            api_key = os.getenv("ANTHROPIC_API_KEY")
            if not api_key:
                print("⚠️ ANTHROPIC_API_KEY not found. Using demo mode.")
                return None
            
            lm = dspy.LM(f"anthropic/{model}", api_key=api_key)
            
        elif provider == "local":
            # Local model integration (Ollama, vLLM, etc.)
            lm = dspy.LM(f"ollama/{model}")
            
        else:
            raise ValueError(f"Unsupported provider: {provider}")
        
        # Configure DSPy globally
        dspy.settings.configure(lm=lm)
        return lm

# ============================================================================
# MODULE 2-3: ADVANCED DSPY SIGNATURES & MODULES
# ============================================================================

class ResearchQuerySignature(dspy.Signature):
    """
    📝 ADVANCED SIGNATURE: Research query processing with context engineering
    
    Demonstrates Module 4 context engineering principles:
    - Clear input/output specification
    - Rich contextual descriptions
    - Quality-focused output constraints
    """
    
    # Input fields with rich context
    research_query = dspy.InputField(
        desc="User's research question or topic requiring comprehensive investigation"
    )
    context_level = dspy.InputField(
        desc="Required depth: 'quick_overview', 'detailed_analysis', or 'comprehensive_research'"
    )
    domain_focus = dspy.InputField(
        desc="Primary domain or field of study for specialized context"
    )
    
    # Output fields with constraints
    research_plan = dspy.OutputField(
        desc="Structured plan outlining research approach and information sources needed"
    )
    key_questions = dspy.OutputField(
        desc="List of 3-5 specific questions that will guide the research process"
    )
    search_strategy = dspy.OutputField(
        desc="Prioritized strategy for information gathering and source evaluation"
    )

class InformationSynthesisSignature(dspy.Signature):
    """📊 SYNTHESIS SIGNATURE: Multi-source information integration"""
    
    collected_information = dspy.InputField(
        desc="Raw information gathered from multiple sources during research"
    )
    research_questions = dspy.InputField(
        desc="Original research questions to guide synthesis focus"
    )
    quality_criteria = dspy.InputField(
        desc="Standards for information quality, relevance, and credibility"
    )
    
    synthesized_analysis = dspy.OutputField(
        desc="Comprehensive analysis integrating information from all sources"
    )
    evidence_quality = dspy.OutputField(
        desc="Assessment of source reliability and evidence strength"
    )
    research_gaps = dspy.OutputField(
        desc="Identified areas requiring additional investigation"
    )

# ============================================================================
# MODULE 5: ADVANCED REASONING PATTERNS INTEGRATION
# ============================================================================

class ResearchPlanningModule(dspy.Module):
    """
    🧠 REASONING MODULE: Chain-of-Thought research planning
    
    Integrates Module 5 reasoning patterns:
    - Chain of Thought for systematic planning
    - Step-by-step research methodology
    - Quality assessment and validation
    """
    
    def __init__(self):
        super().__init__()
        self.planner = dspy.ChainOfThought(ResearchQuerySignature)
        
        # Add reasoning validation
        self.plan_validator = dspy.ChainOfThought(
            "research_plan, feasibility_check -> plan_quality, improvement_suggestions"
        )
    
    def forward(self, research_query: str, context_level: str = "detailed_analysis", 
                domain_focus: str = "general"):
        """Generate comprehensive research plan with reasoning validation"""
        
        # Primary research planning
        plan_result = self.planner(
            research_query=research_query,
            context_level=context_level,
            domain_focus=domain_focus
        )
        
        # Validate plan quality
        validation = self.plan_validator(
            research_plan=plan_result.research_plan,
            feasibility_check="Assess plan completeness, resource requirements, and achievability"
        )
        
        return {
            "research_plan": plan_result.research_plan,
            "key_questions": plan_result.key_questions,
            "search_strategy": plan_result.search_strategy,
            "plan_quality": validation.plan_quality,
            "improvements": validation.improvement_suggestions
        }

# ============================================================================
# MODULE 6: RETRIEVAL-AUGMENTED GENERATION (RAG)
# ============================================================================

class WebSearchTool:
    """
    🌐 EXTERNAL TOOL: Web search integration for RAG
    
    Demonstrates real-world tool integration with error handling
    """
    
    @staticmethod
    def search(query: str, num_results: int = 5) -> List[Dict]:
        """
        Simulated web search (replace with actual API like Brave, Bing, or SerpAPI)
        """
        # In production, integrate with actual search APIs
        simulated_results = [
            {
                "title": f"Research Result {i+1} for: {query}",
                "snippet": f"Detailed information about {query} from academic source {i+1}. "
                          f"This includes comprehensive analysis and verified data points.",
                "url": f"https://academic-source-{i+1}.edu/research/{query.replace(' ', '-')}",
                "credibility": "high" if i < 3 else "medium"
            }
            for i in range(num_results)
        ]
        
        return simulated_results

class RAGModule(dspy.Module):
    """
    📚 RAG INTEGRATION: Information retrieval and synthesis
    
    Combines external search with DSPy processing for comprehensive research
    """
    
    def __init__(self):
        super().__init__()
        self.search_tool = WebSearchTool()
        self.synthesizer = dspy.ChainOfThought(InformationSynthesisSignature)
        
        # Quality assessment module
        self.quality_assessor = dspy.ChainOfThought(
            "source_information, credibility_indicators -> quality_score, reliability_assessment"
        )
    
    def forward(self, research_questions: List[str], search_strategy: str) -> Dict:
        """Execute research with quality assessment"""
        
        all_information = []
        
        # Gather information for each research question
        for question in research_questions:
            search_results = self.search_tool.search(question, num_results=3)
            
            # Process each result
            for result in search_results:
                info_package = {
                    "question": question,
                    "source": result["title"],
                    "content": result["snippet"],
                    "url": result["url"],
                    "credibility": result["credibility"]
                }
                all_information.append(info_package)
        
        # Synthesize all collected information
        synthesis_result = self.synthesizer(
            collected_information=json.dumps(all_information, indent=2),
            research_questions=", ".join(research_questions),
            quality_criteria="Academic credibility, factual accuracy, source diversity, recency"
        )
        
        # Assess overall quality
        quality_assessment = self.quality_assessor(
            source_information=synthesis_result.synthesized_analysis,
            credibility_indicators="Source diversity, academic authority, evidence quality"
        )
        
        return {
            "synthesized_analysis": synthesis_result.synthesized_analysis,
            "evidence_quality": synthesis_result.evidence_quality,
            "research_gaps": synthesis_result.research_gaps,
            "quality_score": quality_assessment.quality_score,
            "reliability": quality_assessment.reliability_assessment,
            "raw_sources": all_information
        }

# ============================================================================
# MODULE 7: AGENTIC BEHAVIOR PATTERNS
# ============================================================================

class AgenticDecisionModule(dspy.Module):
    """
    🤖 AGENTIC BEHAVIOR: Autonomous decision making and tool selection
    
    Implements Module 7 agentic patterns:
    - Autonomous tool selection
    - Multi-step planning
    - Self-monitoring and adaptation
    """
    
    def __init__(self):
        super().__init__()
        
        # Decision-making components
        self.action_planner = dspy.ChainOfThought(
            "current_state, available_tools, research_goal -> next_action, tool_selection, reasoning"
        )
        
        self.progress_monitor = dspy.ChainOfThought(
            "completed_actions, research_progress, goal_completion -> progress_assessment, next_steps"
        )
        
        # Available tools registry
        self.available_tools = {
            "web_search": "Search the internet for current information",
            "analysis": "Perform detailed analysis of gathered information", 
            "synthesis": "Combine multiple sources into coherent insights",
            "validation": "Check quality and credibility of information"
        }
    
    def forward(self, research_goal: str, current_progress: Dict, 
                available_information: List[Dict]) -> Dict:
        """Make autonomous decisions about next research actions"""
        
        # Assess current state
        current_state = f"""
        Research Goal: {research_goal}
        Completed Actions: {len(available_information)} information sources gathered
        Progress: {current_progress.get('completion_percentage', 0)}% complete
        """
        
        # Plan next action
        action_decision = self.action_planner(
            current_state=current_state,
            available_tools=str(self.available_tools),
            research_goal=research_goal
        )
        
        # Monitor progress
        progress_check = self.progress_monitor(
            completed_actions=json.dumps(available_information),
            research_progress=str(current_progress),
            goal_completion=research_goal
        )
        
        return {
            "recommended_action": action_decision.next_action,
            "selected_tool": action_decision.tool_selection,
            "reasoning": action_decision.reasoning,
            "progress_assessment": progress_check.progress_assessment,
            "next_steps": progress_check.next_steps
        }

# ============================================================================
# MODULE 8: PRODUCTION FEATURES & MONITORING
# ============================================================================

class ProductionMonitor:
    """
    📊 PRODUCTION MONITORING: Error handling, logging, performance tracking
    
    Implements Module 8 production deployment patterns
    """
    
    def __init__(self):
        self.performance_metrics = {}
        self.error_log = []
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger("DSPyAgent")
    
    def log_operation(self, operation: str, duration: float, success: bool, 
                     details: Optional[Dict] = None):
        """Track operation performance and errors"""
        
        timestamp = datetime.now().isoformat()
        
        if operation not in self.performance_metrics:
            self.performance_metrics[operation] = {
                "total_calls": 0,
                "success_count": 0,
                "avg_duration": 0,
                "error_count": 0
            }
        
        metrics = self.performance_metrics[operation]
        metrics["total_calls"] += 1
        
        if success:
            metrics["success_count"] += 1
            # Update rolling average
            current_avg = metrics["avg_duration"]
            total_success = metrics["success_count"]
            metrics["avg_duration"] = ((current_avg * (total_success - 1)) + duration) / total_success
        else:
            metrics["error_count"] += 1
            self.error_log.append({
                "timestamp": timestamp,
                "operation": operation,
                "details": details or {}
            })
        
        self.logger.info(f"Operation: {operation}, Duration: {duration:.2f}s, Success: {success}")
    
    def get_health_report(self) -> Dict:
        """Generate system health report"""
        
        total_operations = sum(m["total_calls"] for m in self.performance_metrics.values())
        total_errors = sum(m["error_count"] for m in self.performance_metrics.values())
        
        return {
            "total_operations": total_operations,
            "overall_success_rate": ((total_operations - total_errors) / total_operations * 100) if total_operations > 0 else 0,
            "recent_errors": self.error_log[-5:],  # Last 5 errors
            "performance_by_operation": self.performance_metrics
        }

# ============================================================================
# INTEGRATED AGENTIC RESEARCH ASSISTANT
# ============================================================================

class AgenticResearchAssistant:
    """
    🏆 COMPLETE INTEGRATION: All workshop modules in production-ready agent
    
    Demonstrates:
    - DSPy signatures and modules (Modules 2-3)
    - Context engineering (Module 4)  
    - Reasoning patterns (Module 5)
    - RAG integration (Module 6)
    - Agentic behavior (Module 7)
    - Production features (Module 8)
    """
    
    def __init__(self, llm_provider: str = "openai", model: str = "gpt-4o-mini"):
        # Configure LLM
        self.llm = LLMConfig.setup_llm(llm_provider, model)
        
        # Initialize core modules
        self.research_planner = ResearchPlanningModule()
        self.rag_module = RAGModule()
        self.agent_decision = AgenticDecisionModule()
        self.monitor = ProductionMonitor()
        
        # Research state
        self.current_research = None
        self.research_history = []
    
    def conduct_research(self, research_query: str, 
                        context_level: str = "detailed_analysis",
                        domain_focus: str = "general") -> Dict[str, Any]:
        """
        🎯 MAIN RESEARCH FUNCTION: Complete research workflow
        
        Integrates all modules for comprehensive research assistance
        """
        
        import time
        start_time = time.time()
        
        try:
            self.monitor.logger.info(f"Starting research: {research_query}")
            
            # PHASE 1: Research Planning (Modules 2-3, 5)
            planning_start = time.time()
            
            research_plan = self.research_planner(
                research_query=research_query,
                context_level=context_level,
                domain_focus=domain_focus
            )
            
            planning_duration = time.time() - planning_start
            self.monitor.log_operation("research_planning", planning_duration, True)
            
            # PHASE 2: Information Gathering (Module 6 - RAG)
            rag_start = time.time()
            
            # Extract questions for search
            questions = research_plan["key_questions"].split("\n") if isinstance(research_plan["key_questions"], str) else research_plan["key_questions"]
            if isinstance(questions, str):
                questions = [q.strip() for q in questions.split(",") if q.strip()]
            
            research_results = self.rag_module(
                research_questions=questions[:3],  # Limit to first 3 questions
                search_strategy=research_plan["search_strategy"]
            )
            
            rag_duration = time.time() - rag_start
            self.monitor.log_operation("information_gathering", rag_duration, True)
            
            # PHASE 3: Agentic Decision Making (Module 7)
            agent_start = time.time()
            
            current_progress = {
                "completion_percentage": 75,  # Simulated progress
                "sources_gathered": len(research_results["raw_sources"]),
                "quality_score": research_results["quality_score"]
            }
            
            agent_decision = self.agent_decision(
                research_goal=research_query,
                current_progress=current_progress,
                available_information=research_results["raw_sources"]
            )
            
            agent_duration = time.time() - agent_start
            self.monitor.log_operation("agentic_decision", agent_duration, True)
            
            # PHASE 4: Final Integration and Quality Assurance
            final_result = {
                "research_query": research_query,
                "research_plan": research_plan,
                "findings": {
                    "synthesized_analysis": research_results["synthesized_analysis"],
                    "evidence_quality": research_results["evidence_quality"],
                    "research_gaps": research_results["research_gaps"]
                },
                "quality_assessment": {
                    "overall_score": research_results["quality_score"],
                    "reliability": research_results["reliability"],
                    "source_count": len(research_results["raw_sources"])
                },
                "agent_recommendations": {
                    "next_action": agent_decision["recommended_action"],
                    "reasoning": agent_decision["reasoning"],
                    "progress_assessment": agent_decision["progress_assessment"]
                },
                "performance_metrics": {
                    "total_duration": time.time() - start_time,
                    "planning_time": planning_duration,
                    "research_time": rag_duration,
                    "decision_time": agent_duration
                }
            }
            
            # Store research history
            self.current_research = final_result
            self.research_history.append({
                "timestamp": datetime.now().isoformat(),
                "query": research_query,
                "success": True,
                "duration": time.time() - start_time
            })
            
            self.monitor.log_operation("complete_research", time.time() - start_time, True)
            return final_result
            
        except Exception as e:
            error_duration = time.time() - start_time
            self.monitor.log_operation("complete_research", error_duration, False, {"error": str(e)})
            
            return {
                "error": f"Research failed: {str(e)}",
                "partial_results": getattr(self, 'current_research', {}),
                "performance_metrics": {
                    "error_duration": error_duration
                }
            }
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system health and performance report"""
        
        health_report = self.monitor.get_health_report()
        
        return {
            "system_health": health_report,
            "llm_status": "connected" if self.llm else "demo_mode",
            "research_history_count": len(self.research_history),
            "last_research": self.research_history[-1] if self.research_history else None,
            "module_status": {
                "research_planner": "active",
                "rag_module": "active", 
                "agent_decision": "active",
                "monitor": "active"
            }
        }

# ============================================================================
# PRACTICAL DEMONSTRATION & USAGE EXAMPLES
# ============================================================================

def demonstrate_practical_agent():
    """
    🎯 COMPLETE PRACTICAL DEMONSTRATION
    
    Shows real-world usage of integrated DSPy agentic system
    """
    
    print("🤖 PRACTICAL DSPY AGENTIC RESEARCH ASSISTANT DEMONSTRATION")
    print("=" * 70)
    
    # Initialize the agent
    print("\n🔧 INITIALIZING AGENT WITH LLM INTEGRATION")
    print("-" * 50)
    
    # Try OpenAI first, fallback to demo mode
    assistant = AgenticResearchAssistant(llm_provider="openai", model="gpt-4o-mini")
    
    if assistant.llm:
        print("✅ LLM connected successfully")
    else:
        print("⚠️ Running in demo mode (API keys not configured)")
        print("💡 To use real LLMs, set OPENAI_API_KEY or ANTHROPIC_API_KEY environment variables")
    
    # Example research queries
    research_examples = [
        {
            "query": "What are the latest developments in renewable energy technology for 2024?",
            "context_level": "detailed_analysis",
            "domain_focus": "renewable energy"
        },
        {
            "query": "How does artificial intelligence impact modern healthcare delivery?",
            "context_level": "comprehensive_research", 
            "domain_focus": "healthcare AI"
        }
    ]
    
    for i, example in enumerate(research_examples, 1):
        print(f"\n📊 RESEARCH EXAMPLE {i}")
        print("-" * 30)
        print(f"Query: {example['query']}")
        
        # Conduct research
        result = assistant.conduct_research(**example)
        
        if "error" not in result:
            print(f"✅ Research completed successfully")
            print(f"📝 Analysis: {result['findings']['synthesized_analysis'][:200]}...")
            print(f"🎯 Quality Score: {result['quality_assessment']['overall_score']}")
            print(f"⚡ Duration: {result['performance_metrics']['total_duration']:.2f} seconds")
            
            # Show agent recommendations
            print(f"🤖 Agent Recommendation: {result['agent_recommendations']['next_action']}")
        else:
            print(f"❌ Research failed: {result['error']}")
    
    # System health report
    print(f"\n📈 SYSTEM HEALTH REPORT")
    print("-" * 30)
    
    status = assistant.get_system_status()
    health = status["system_health"]
    
    print(f"Total Operations: {health['total_operations']}")
    print(f"Success Rate: {health['overall_success_rate']:.1f}%")
    print(f"LLM Status: {status['llm_status']}")
    print(f"Research History: {status['research_history_count']} queries")
    
    # Show recent performance
    if health["performance_by_operation"]:
        print("\n⚡ Performance by Operation:")
        for operation, metrics in health["performance_by_operation"].items():
            success_rate = (metrics["success_count"] / metrics["total_calls"] * 100) if metrics["total_calls"] > 0 else 0
            print(f"   {operation}: {success_rate:.1f}% success, {metrics['avg_duration']:.2f}s avg")
    
    print("\n🎉 DEMONSTRATION COMPLETE!")
    print("\n💡 NEXT STEPS FOR PRODUCTION DEPLOYMENT:")
    print("   1. Configure real LLM API keys")
    print("   2. Integrate actual search APIs (SerpAPI, Brave, etc.)")
    print("   3. Add domain-specific tools and knowledge bases")
    print("   4. Implement user interface (Streamlit, FastAPI, etc.)")
    print("   5. Deploy with monitoring and scaling infrastructure")
    
    return assistant

# Production usage example
if __name__ == "__main__":
    # Set up environment variables for production use:
    # export OPENAI_API_KEY="your-api-key-here"
    # export ANTHROPIC_API_KEY="your-api-key-here"
    
    agent = demonstrate_practical_agent()