# ============================================================================
# 🎓 DSPy SOCRATIC LEARNING JOURNEY: FROM PROMPTING TO PROGRAMMING
# A Student-Friendly Discovery Method for Understanding DSPy's Revolution
# ============================================================================

import dspy
import json
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime

# ============================================================================
# 🚨 STEP 1: THE OLD WAY - SHOWING THE PROBLEMS
# ============================================================================

class TraditionalPromptingApproach:
    """
    🚨 SOCRATIC QUESTION: "Why does this approach break down?"
    
    Let's start by seeing what DOESN'T work, so you understand why DSPy is revolutionary.
    """
    
    def __init__(self):
        # ❌ PROBLEM 1: Static prompts can't adapt
        self.document_analysis_prompt = """
        Analyze this document and provide:
        1. A summary
        2. Key findings  
        3. Recommendations
        
        Document: {document}
        
        Please format your response exactly as shown above.
        """
        
        # ❌ PROBLEM 2: Different prompts for different scenarios
        self.business_prompt = "Analyze this business document..."
        self.legal_prompt = "Analyze this legal document..."
        self.technical_prompt = "Analyze this technical document..."
        # ... and dozens more!
    
    def analyze_document(self, document: str, document_type: str = "general"):
        """
        🚨 SOCRATIC CHALLENGE: "Can you spot all the problems here?"
        
        Problems to discover:
        1. How do you know which prompt to use?
        2. What if the format doesn't match your expectation?
        3. How do you improve when it doesn't work?
        4. How do you handle edge cases?
        5. How do you ensure consistency across different documents?
        """
        
        # ❌ BRITTLE: Manual prompt selection
        if document_type == "business":
            prompt = self.business_prompt
        elif document_type == "legal":
            prompt = self.legal_prompt
        else:
            prompt = self.document_analysis_prompt
        
        # ❌ FRAGILE: Hope the LLM follows the format
        formatted_prompt = prompt.format(document=document)
        
        # ❌ NO OPTIMIZATION: If it doesn't work, you're stuck manually fixing
        return f"""
        TRADITIONAL PROMPT RESULT:
        This approach has fundamental problems:
        - Static prompts can't adapt to different document types
        - Manual prompt engineering for every scenario
        - No systematic way to improve performance
        - Brittle error handling and inconsistent outputs
        - You're basically hoping and praying it works!
        """

# ============================================================================
# 🌟 STEP 2: THE DSPY WAY - SYSTEMATIC PROGRAMMING APPROACH
# ============================================================================

# 🔍 SOCRATIC DISCOVERY: "What if we separated WHAT from HOW?"

class DocumentAnalysisSignature(dspy.Signature):
    """
    ✨ SOCRATIC INSIGHT: "This is a BLUEPRINT, not instructions!"
    
    🤔 GUIDING QUESTIONS:
    Q: "What do you notice about this compared to the traditional prompt?"
    A: "It defines WHAT you want, not HOW to ask for it!"
    
    Q: "Who do you think writes the actual prompts?"
    A: "DSPy does it automatically!"
    
    🎯 KEY INSIGHT: You're programming the INTERFACE, not the implementation
    """
    
    # 📋 INPUTS: What information goes into the system
    document = dspy.InputField(
        desc="The document text that needs to be analyzed"
    )
    analysis_type = dspy.InputField(
        desc="Type of analysis needed (business, legal, technical, general)"
    )
    
    # 📊 OUTPUTS: What information comes out of the system
    summary = dspy.OutputField(
        desc="A concise, accurate summary of the main points"
    )
    key_findings = dspy.OutputField(
        desc="The most important discoveries or insights from the document"
    )
    recommendations = dspy.OutputField(
        desc="Actionable next steps based on the analysis"
    )
    confidence_score = dspy.OutputField(
        desc="How confident the analysis is, from 0.0 to 1.0"
    )

# 🔍 SOCRATIC EXPLORATION: "How does DSPy use this signature?"

class DSPyDocumentAnalyzer(dspy.Module):
    """
    🧠 SOCRATIC DISCOVERY: "This is where the magic happens!"
    
    🤔 GUIDING QUESTIONS TO ASK STUDENTS:
    
    Q: "What do you think ChainOfThought does differently than a simple prediction?"
    A: "It makes the AI think step-by-step, like a human analyst!"
    
    Q: "Who writes the actual prompts that get sent to the LLM?"
    A: "DSPy automatically generates and optimizes them!"
    
    Q: "What happens if the analysis isn't good enough?"
    A: "DSPy can automatically improve it through optimization!"
    """
    
    def __init__(self):
        super().__init__()
        
        # 🔗 CHAIN-OF-THOUGHT: Makes the AI think step-by-step
        self.analyzer = dspy.ChainOfThought(DocumentAnalysisSignature)
        
        # 📊 TRACK PERFORMANCE: For optimization later
        self.analysis_history = []
        
    def forward(self, document: str, analysis_type: str = "general"):
        """
        ✨ SOCRATIC MOMENT: "Watch DSPy work its magic!"
        
        Behind the scenes, DSPy:
        1. Takes your signature definition
        2. Generates optimized prompts automatically
        3. Handles the LLM interaction
        4. Ensures output follows your specified format
        5. Can improve over time with optimization!
        """
        
        # 🎯 ONE LINE OF CODE: DSPy handles all the complexity
        result = self.analyzer(
            document=document,
            analysis_type=analysis_type
        )
        
        # 📝 STORE FOR LEARNING: This enables optimization
        analysis_record = {
            "timestamp": datetime.now().isoformat(),
            "document_length": len(document),
            "analysis_type": analysis_type,
            "confidence": result.confidence_score,
            "result": {
                "summary": result.summary,
                "key_findings": result.key_findings,
                "recommendations": result.recommendations
            }
        }
        self.analysis_history.append(analysis_record)
        
        return result

# ============================================================================
# 🔍 STEP 3: SOCRATIC COMPARISON - DISCOVERING THE DIFFERENCES
# ============================================================================

class SocraticComparison:
    """
    🎓 DISCOVERY-BASED LEARNING: Let students see the differences themselves
    """
    
    def __init__(self):
        self.traditional = TraditionalPromptingApproach()
        self.dspy_analyzer = DSPyDocumentAnalyzer()
    
    def interactive_discovery(self, document_text: str):
        """
        🔍 SOCRATIC METHOD: Ask questions that lead to insights
        """
        
        print("🎓 SOCRATIC DISCOVERY SESSION")
        print("=" * 50)
        print("Let's analyze the same document using both approaches...")
        print("Pay attention to the differences!")
        
        # TRADITIONAL APPROACH
        print("\n❌ TRADITIONAL PROMPTING APPROACH:")
        print("-" * 30)
        traditional_result = self.traditional.analyze_document(document_text)
        print(traditional_result)
        
        # DSPY APPROACH  
        print("\n✅ DSPY PROGRAMMING APPROACH:")
        print("-" * 30)
        dspy_result = self.dspy_analyzer(document_text, "business")
        
        print(f"📝 Summary: {dspy_result.summary}")
        print(f"🔍 Key Findings: {dspy_result.key_findings}")
        print(f"💡 Recommendations: {dspy_result.recommendations}")
        print(f"📊 Confidence: {dspy_result.confidence_score}")
        
        # SOCRATIC QUESTIONS FOR REFLECTION
        self._ask_discovery_questions()
    
    def _ask_discovery_questions(self):
        """
        🤔 SOCRATIC QUESTIONS: Guide students to insights
        """
        
        print("\n🤔 DISCOVERY QUESTIONS:")
        print("=" * 30)
        
        questions = [
            "Which approach gives you more structured, reliable output?",
            "Which approach would be easier to debug when something goes wrong?",
            "Which approach could handle new document types without rewriting code?",
            "Which approach could automatically get better over time?",
            "If you had to build a production system, which would you choose?"
        ]
        
        for i, question in enumerate(questions, 1):
            print(f"{i}. {question}")
        
        print("\n💡 Think about these as we explore DSPy's secret weapon...")

# ============================================================================
# 🚀 STEP 4: THE SECRET WEAPON - SELF-IMPROVING SYSTEMS
# ============================================================================

class DSPyOptimizationDemo:
    """
    🚀 THE BIG REVEAL: "DSPy systems can automatically get better!"
    
    🤔 SOCRATIC SETUP: "What if your AI could learn from its mistakes and 
    automatically improve its own prompts?"
    """
    
    def __init__(self):
        self.analyzer = DSPyDocumentAnalyzer()
        
        # 📊 SAMPLE TRAINING DATA: Examples of good analysis
        self.training_examples = [
            dspy.Example(
                document="This quarterly report shows revenue increased by 15%...",
                analysis_type="business",
                summary="Strong quarterly performance with 15% revenue growth",
                key_findings="Revenue growth driven by new product launches",
                recommendations="Continue investment in product development"
            ),
            dspy.Example(
                document="The contract specifies delivery within 30 days...",
                analysis_type="legal", 
                summary="Standard delivery contract with 30-day timeline",
                key_findings="Clear delivery obligations and penalty clauses",
                recommendations="Review penalty terms before signing"
            ),
            # Add more examples for better optimization...
        ]
    
    def demonstrate_optimization_magic(self):
        """
        ✨ THE MAGIC MOMENT: "Watch DSPy improve itself!"
        
        🎯 SOCRATIC BUILDUP:
        Q: "What if mistakes could make the system smarter?"
        Q: "What if the system could find better ways to ask questions?"
        Q: "What if it could adapt to your specific use case automatically?"
        
        A: "That's exactly what DSPy optimization does!"
        """
        
        print("🚀 DSPY OPTIMIZATION DEMONSTRATION")
        print("=" * 50)
        print("Watch DSPy automatically improve its own prompts!")
        
        # 🔄 BEFORE OPTIMIZATION
        print("\n📊 BEFORE OPTIMIZATION:")
        print("Using default DSPy-generated prompts...")
        
        test_document = """
        Q3 financial results show mixed performance. Revenue grew 8% but costs 
        increased 12%. Customer satisfaction dropped to 7.2/10. New product 
        launch delayed until Q4. Recommend strategic review.
        """
        
        before_result = self.analyzer(test_document, "business")
        print(f"Summary: {before_result.summary[:100]}...")
        print(f"Confidence: {before_result.confidence_score}")
        
        # ⚙️ THE OPTIMIZATION PROCESS
        print("\n⚙️ RUNNING OPTIMIZATION...")
        print("DSPy is analyzing training examples...")
        print("Finding better ways to prompt the LLM...")
        print("Testing different approaches...")
        
        # 🎯 CREATE OPTIMIZER
        optimizer = dspy.BootstrapFewShot(
            metric=self._accuracy_metric,
            max_bootstrapped_demos=3,
            max_labeled_demos=2
        )
        
        # 🚀 COMPILE (OPTIMIZE) THE MODULE
        print("Compiling optimized version...")
        optimized_analyzer = optimizer.compile(
            self.analyzer,
            trainset=self.training_examples
        )
        
        # ✅ AFTER OPTIMIZATION
        print("\n✅ AFTER OPTIMIZATION:")
        print("Using DSPy-optimized prompts...")
        
        after_result = optimized_analyzer(test_document, "business")
        print(f"Summary: {after_result.summary[:100]}...")
        print(f"Confidence: {after_result.confidence_score}")
        
        # 🎉 THE REVELATION
        print("\n🎉 THE REVELATION:")
        print("=" * 30)
        print("DSPy automatically:")
        print("✨ Generated better prompts for your specific use case")
        print("✨ Learned from your training examples")
        print("✨ Improved the quality of analysis")
        print("✨ Did this WITHOUT you writing a single prompt!")
        
        return optimized_analyzer
    
    def _accuracy_metric(self, example, prediction, trace=None):
        """
        📊 SOCRATIC INSIGHT: "How does DSPy know what 'better' means?"
        
        Answer: You define the success criteria, DSPy optimizes toward it!
        """
        # Simple metric: check if key terms appear in the right places
        summary_quality = len(example.summary.split()) > 5
        findings_present = len(prediction.key_findings) > 20
        recommendations_actionable = "recommend" in prediction.recommendations.lower()
        
        return summary_quality and findings_present and recommendations_actionable

# ============================================================================
# 🎓 STEP 5: COMPLETE SOCRATIC LEARNING JOURNEY
# ============================================================================

class CompleteSocraticJourney:
    """
    🎓 THE COMPLETE LEARNING EXPERIENCE
    
    Takes students from "I don't get it" to "This is revolutionary!"
    """
    
    def __init__(self):
        self.comparison = SocraticComparison()
        self.optimization_demo = DSPyOptimizationDemo()
    
    def run_complete_journey(self):
        """
        🚀 COMPLETE DISCOVERY JOURNEY
        """
        
        print("🎓 WELCOME TO THE DSPY DISCOVERY JOURNEY!")
        print("=" * 60)
        print("We're going to discover WHY DSPy is revolutionary...")
        print("Not by me telling you, but by you SEEING it yourself!")
        
        # PHASE 1: PROBLEM DISCOVERY
        print("\n🔍 PHASE 1: DISCOVERING THE PROBLEM")
        print("-" * 40)
        
        sample_document = """
        Executive Summary: Our Q3 performance shows significant challenges. 
        Revenue declined 8% quarter-over-quarter, primarily due to supply chain 
        disruptions and increased competition. Customer acquisition costs rose 
        25% while retention dropped to 78%. Immediate action required on 
        pricing strategy and operational efficiency.
        """
        
        self.comparison.interactive_discovery(sample_document)
        
        # PHASE 2: SOLUTION REVELATION  
        print("\n🚀 PHASE 2: THE OPTIMIZATION REVELATION")
        print("-" * 40)
        
        optimized_system = self.optimization_demo.demonstrate_optimization_magic()
        
        # PHASE 3: MINDSET TRANSFORMATION
        print("\n🧠 PHASE 3: MINDSET TRANSFORMATION COMPLETE")
        print("-" * 40)
        
        self._final_socratic_synthesis()
        
        return optimized_system
    
    def _final_socratic_synthesis(self):
        """
        🧠 FINAL SOCRATIC QUESTIONS: Cementing the understanding
        """
        
        print("\n🤔 FINAL REFLECTION QUESTIONS:")
        print("=" * 40)
        
        print("\nThink about what you just experienced:")
        print("1. How has your thinking about AI development changed?")
        print("2. What's the fundamental difference between prompting and programming with DSPy?")
        print("3. Why is automatic optimization so powerful?")
        print("4. How would this change the way you build AI applications?")
        
        print("\n💡 KEY INSIGHTS YOU'VE DISCOVERED:")
        print("✨ DSPy separates WHAT you want from HOW to get it")
        print("✨ Signatures define interfaces, Modules implement logic")
        print("✨ Optimization automatically improves performance")
        print("✨ You're programming SYSTEMS, not crafting prompts")
        print("✨ Self-improving AI is possible and practical!")
        
        print("\n🎉 CONGRATULATIONS!")
        print("You've discovered the future of AI development!")

# ============================================================================
# 🎯 INTERACTIVE DEMONSTRATION RUNNER
# ============================================================================

def run_socratic_dspy_lesson():
    """
    🎓 MAIN TEACHING FUNCTION
    
    This runs the complete Socratic discovery journey
    """
    
    print("🎯 Starting your DSPy discovery journey...")
    print("Remember: The best learning happens when YOU discover the insights!")
    print()
    
    # Set up DSPy (you'd need to configure this with real credentials)
    try:
        # dspy.settings.configure(lm=dspy.OpenAI(model="gpt-3.5-turbo"))
        print("⚠️  DSPy configuration needed for full demo")
        print("For now, we'll simulate the results...")
    except:
        print("💡 Running in simulation mode for demonstration")
    
    # Run the complete journey
    journey = CompleteSocraticJourney()
    result = journey.run_complete_journey()
    
    print("\n🎓 SOCRATIC JOURNEY COMPLETE!")
    print("You've experienced the DSPy revolution firsthand!")
    
    return result

# Additional Socratic Questions for Further Exploration
def advanced_socratic_questions():
    """
    🔬 ADVANCED DISCOVERY QUESTIONS
    
    For students ready to go deeper...
    """
    
    questions = [
        {
            "category": "Architecture Understanding",
            "question": "If Signatures are like function definitions, what are Modules like in traditional programming?",
            "hint": "Think about classes and methods..."
        },
        {
            "category": "Optimization Insight", 
            "question": "Why can DSPy optimize prompts better than humans manually iterating?",
            "hint": "Think about systematic vs. ad-hoc approaches..."
        },
        {
            "category": "Scaling Implications",
            "question": "How would building a system with 50 different AI components be different with DSPy vs traditional prompting?",
            "hint": "Think about maintenance and consistency..."
        },
        {
            "category": "Future Possibilities",
            "question": "If AI systems can optimize their own prompts, what other aspects could they potentially optimize?",
            "hint": "Think about reasoning patterns, retrieval strategies, even architecture..."
        }
    ]
    
    print("\n🔬 ADVANCED EXPLORATION QUESTIONS:")
    print("=" * 40)
    
    for q in questions:
        print(f"\n📋 {q['category']}:")
        print(f"   🤔 {q['question']}")
        print(f"   💡 {q['hint']}")

if __name__ == "__main__":
    # Run the complete Socratic learning experience
    run_socratic_dspy_lesson()
    
    # Provide advanced questions for deeper exploration
    advanced_socratic_questions()
    
    print("\n🚀 Ready to build your own DSPy applications?")
    print("You now understand the fundamental paradigm shift!")
    print("From prompting to programming... from hoping to optimizing!")