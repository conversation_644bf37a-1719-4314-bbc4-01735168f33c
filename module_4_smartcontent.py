# 🧠 SMART CONTEXT SYSTEMS: Advanced DSPy Implementation
"""
🎯 PHASE I: SYSTEMATIC DECONSTRUCTION OF CONTEXT ARCHITECTURE

CRITICAL ANALYSIS: Context engineering represents the evolution from:
- Static prompts → Dynamic information systems
- Manual curation → Intelligent assembly  
- Single-shot queries → Contextual conversations
- Generic responses → Personalized intelligence

UNDERLYING DYNAMICS:
1. Context Relevance Scoring: Not all information is equally valuable
2. Dynamic Assembly Patterns: Context structure affects reasoning quality
3. Temporal Context Evolution: Information relevance changes over time
4. Multi-dimensional Context: Text, metadata, user history, task requirements

📊 COMPLEXITY PROGRESSION:
Level 1: Basic context retrieval and assembly
Level 2: Intelligent context ranking and filtering
Level 3: Adaptive context strategies based on task type
Level 4: Self-optimizing context systems with feedback loops
"""

import dspy
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from abc import ABC, abstractmethod
import hashlib
import time

# 🔍 PHASE I: ANALYTICAL FOUNDATION

class ContextType(Enum):
    """🧩 STRATEGIC CATEGORIZATION: Different context types serve different cognitive functions"""
    FACTUAL = "factual_knowledge"           # Hard facts and data
    PROCEDURAL = "procedural_knowledge"     # How-to information
    EXPERIENTIAL = "experiential_knowledge" # Past experiences and examples
    CONTEXTUAL = "situational_context"      # Current situation details
    RELATIONAL = "relational_context"       # Connections and relationships
    TEMPORAL = "temporal_context"           # Time-sensitive information

class ContextRelevanceStrategy(Enum):
    """📊 ANALYTICAL APPROACH: Different strategies for determining context relevance"""
    SEMANTIC_SIMILARITY = "semantic_similarity"
    TASK_ALIGNMENT = "task_alignment"
    TEMPORAL_PROXIMITY = "temporal_proximity"
    USER_PREFERENCE = "user_preference"
    PERFORMANCE_FEEDBACK = "performance_feedback"
    HYBRID_SCORING = "hybrid_scoring"

@dataclass
class ContextElement:
    """🔧 STRUCTURED REPRESENTATION: Atomic unit of contextual information"""
    content: str
    context_type: ContextType
    relevance_score: float
    source: str
    timestamp: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    usage_history: List[str] = field(default_factory=list)
    performance_impact: Optional[float] = None

@dataclass
class ContextAssemblyResult:
    """📋 COMPREHENSIVE OUTPUT: Complete context assembly with quality metrics"""
    assembled_context: str
    context_elements: List[ContextElement]
    assembly_strategy: str
    quality_metrics: Dict[str, float]
    optimization_suggestions: List[str]
    processing_metadata: Dict[str, Any]

# 🏗️ PHASE I: ARCHITECTURAL DECONSTRUCTION

class IntelligentContextRetriever(dspy.Module):
    """
    🔍 ANALYTICAL COMPONENT: Sophisticated context retrieval with multi-dimensional scoring
    
    CRITICAL INSIGHT: Context retrieval isn't just about similarity - it's about
    strategic information assembly based on task requirements and cognitive load optimization
    """
    
    def __init__(self, retrieval_strategy: ContextRelevanceStrategy = ContextRelevanceStrategy.HYBRID_SCORING):
        super().__init__()
        
        # Core retrieval components
        self.semantic_retriever = dspy.Retrieve(k=20)  # Cast wide net initially
        
        # Intelligent scoring signatures
        self.relevance_scorer = dspy.ChainOfThought(
            "query, context_element, task_type -> relevance_score, reasoning"
        )
        
        self.context_quality_assessor = dspy.ChainOfThought(
            "context_elements, query, task_requirements -> quality_assessment, improvement_suggestions"
        )
        
        self.strategy = retrieval_strategy
        self.performance_history = {}
    
    def forward(self, 
                query: str, 
                task_type: str,
                user_context: Dict[str, Any],
                max_context_elements: int = 10) -> List[ContextElement]:
        """
        🎯 STRATEGIC RETRIEVAL: Multi-phase context assembly with intelligent filtering
        
        ANALYTICAL APPROACH:
        1. Broad retrieval to identify candidate information
        2. Multi-dimensional relevance scoring
        3. Strategic selection based on cognitive load optimization
        4. Quality validation and improvement suggestions
        """
        
        # Phase 1: Broad semantic retrieval
        raw_retrieval = self.semantic_retriever(query)
        
        # Phase 2: Intelligent relevance scoring
        scored_elements = []
        for passage in raw_retrieval.passages:
            
            # Generate relevance score with reasoning
            scoring_result = self.relevance_scorer(
                query=query,
                context_element=passage,
                task_type=task_type
            )
            
            # Create structured context element
            element = ContextElement(
                content=passage,
                context_type=self._classify_content_type(passage),
                relevance_score=float(scoring_result.relevance_score),
                source="retrieval_system",
                timestamp=time.time(),
                metadata={
                    "scoring_reasoning": scoring_result.reasoning,
                    "task_type": task_type,
                    "user_context": user_context
                }
            )
            
            scored_elements.append(element)
        
        # Phase 3: Strategic selection and optimization
        optimized_elements = self._optimize_context_selection(
            scored_elements, 
            max_context_elements,
            task_type,
            user_context
        )
        
        # Phase 4: Quality assessment and feedback
        self._assess_and_improve_context_quality(optimized_elements, query, task_type)
        
        return optimized_elements
    
    def _classify_content_type(self, content: str) -> ContextType:
        """🧩 CONTENT CLASSIFICATION: Determine optimal context type for strategic assembly"""
        
        # Simple heuristic classification (could be a full DSPy module)
        content_lower = content.lower()
        
        if any(word in content_lower for word in ['how to', 'step', 'process', 'method']):
            return ContextType.PROCEDURAL
        elif any(word in content_lower for word in ['example', 'case study', 'instance']):
            return ContextType.EXPERIENTIAL
        elif any(word in content_lower for word in ['related to', 'connected', 'relationship']):
            return ContextType.RELATIONAL
        elif any(word in content_lower for word in ['recent', 'current', 'latest', 'now']):
            return ContextType.TEMPORAL
        else:
            return ContextType.FACTUAL
    
    def _optimize_context_selection(self, 
                                  elements: List[ContextElement],
                                  max_elements: int,
                                  task_type: str,
                                  user_context: Dict[str, Any]) -> List[ContextElement]:
        """
        📊 STRATEGIC OPTIMIZATION: Intelligent context selection based on multiple criteria
        
        OPTIMIZATION FACTORS:
        - Relevance score (primary)
        - Context type diversity (avoid redundancy)
        - Cognitive load balance (mix of simple/complex)
        - User preference alignment
        - Performance history feedback
        """
        
        # Sort by relevance score
        sorted_elements = sorted(elements, key=lambda x: x.relevance_score, reverse=True)
        
        # Apply diversity and optimization filters
        optimized_selection = []
        context_types_used = set()
        
        for element in sorted_elements:
            if len(optimized_selection) >= max_elements:
                break
                
            # Diversity consideration: prefer different context types
            type_penalty = 0.1 if element.context_type in context_types_used else 0
            adjusted_score = element.relevance_score - type_penalty
            
            # User preference alignment
            if self._aligns_with_user_preferences(element, user_context):
                adjusted_score += 0.1
            
            # Performance history consideration
            if self._has_positive_performance_history(element, task_type):
                adjusted_score += 0.05
            
            element.relevance_score = adjusted_score
            optimized_selection.append(element)
            context_types_used.add(element.context_type)
        
        return optimized_selection
    
    def _aligns_with_user_preferences(self, element: ContextElement, user_context: Dict[str, Any]) -> bool:
        """🎯 USER ALIGNMENT: Check if context aligns with user preferences and background"""
        user_expertise = user_context.get('expertise_level', 'intermediate')
        
        # Simple alignment heuristics (could be more sophisticated)
        if user_expertise == 'beginner' and element.context_type == ContextType.PROCEDURAL:
            return True
        elif user_expertise == 'expert' and element.context_type == ContextType.FACTUAL:
            return True
        
        return False
    
    def _has_positive_performance_history(self, element: ContextElement, task_type: str) -> bool:
        """📈 PERFORMANCE TRACKING: Leverage historical performance data for optimization"""
        element_hash = hashlib.md5(element.content.encode()).hexdigest()
        history_key = f"{element_hash}_{task_type}"
        
        return self.performance_history.get(history_key, {}).get('success_rate', 0) > 0.7
    
    def _assess_and_improve_context_quality(self, 
                                          elements: List[ContextElement], 
                                          query: str, 
                                          task_type: str):
        """🔧 CONTINUOUS IMPROVEMENT: Assess context quality and generate optimization suggestions"""
        
        # Generate quality assessment
        quality_result = self.context_quality_assessor(
            context_elements=str([e.content[:200] for e in elements]),
            query=query,
            task_requirements=task_type
        )
        
        # Store quality feedback for system improvement
        quality_score = float(quality_result.quality_assessment.split()[0] if quality_result.quality_assessment.split() else "0.7")
        
        for element in elements:
            element.metadata['quality_assessment'] = quality_score
            element.metadata['improvement_suggestions'] = quality_result.improvement_suggestions

class AdaptiveContextAssembler(dspy.Module):
    """
    🏗️ INTELLIGENT ASSEMBLY: Strategic context organization for optimal cognitive processing
    
    CRITICAL DESIGN PRINCIPLE: Context structure affects reasoning quality as much as content
    Different assembly patterns optimize for different cognitive tasks
    """
    
    def __init__(self):
        super().__init__()
        
        # Assembly strategy signatures
        self.structure_optimizer = dspy.ChainOfThought(
            "context_elements, task_type, cognitive_load_target -> optimal_structure, reasoning"
        )
        
        self.coherence_enhancer = dspy.ChainOfThought(
            "assembled_context, task_requirements -> enhanced_context, coherence_improvements"
        )
        
        self.assembly_strategies = {
            'hierarchical': self._hierarchical_assembly,
            'chronological': self._chronological_assembly,
            'importance_first': self._importance_first_assembly,
            'cognitive_load_optimized': self._cognitive_load_assembly
        }
    
    def forward(self, 
                context_elements: List[ContextElement],
                task_type: str,
                assembly_strategy: str = 'cognitive_load_optimized',
                max_tokens: int = 4000) -> ContextAssemblyResult:
        """
        🎯 STRATEGIC ASSEMBLY: Intelligent context structuring for optimal performance
        
        ASSEMBLY OPTIMIZATION FACTORS:
        1. Cognitive load distribution
        2. Information flow coherence  
        3. Task-specific optimization
        4. Token efficiency maximization
        """
        
        # Phase 1: Determine optimal assembly structure
        structure_result = self.structure_optimizer(
            context_elements=str([f"{e.context_type.value}: {e.content[:100]}" for e in context_elements]),
            task_type=task_type,
            cognitive_load_target="optimized_for_reasoning"
        )
        
        # Phase 2: Apply assembly strategy
        assembly_function = self.assembly_strategies.get(assembly_strategy, self._cognitive_load_assembly)
        assembled_context = assembly_function(context_elements, max_tokens)
        
        # Phase 3: Enhance coherence and flow
        coherence_result = self.coherence_enhancer(
            assembled_context=assembled_context,
            task_requirements=task_type
        )
        
        # Phase 4: Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(context_elements, assembled_context)
        
        # Phase 5: Generate optimization suggestions
        optimization_suggestions = self._generate_optimization_suggestions(
            context_elements, 
            assembled_context, 
            quality_metrics
        )
        
        return ContextAssemblyResult(
            assembled_context=coherence_result.enhanced_context,
            context_elements=context_elements,
            assembly_strategy=assembly_strategy,
            quality_metrics=quality_metrics,
            optimization_suggestions=optimization_suggestions,
            processing_metadata={
                'structure_reasoning': structure_result.reasoning,
                'coherence_improvements': coherence_result.coherence_improvements,
                'token_usage': len(assembled_context.split()),
                'assembly_time': time.time()
            }
        )
    
    def _hierarchical_assembly(self, elements: List[ContextElement], max_tokens: int) -> str:
        """📊 HIERARCHICAL STRUCTURE: Organize by importance and logical flow"""
        
        # Group by context type and importance
        grouped = {}
        for element in elements:
            if element.context_type not in grouped:
                grouped[element.context_type] = []
            grouped[element.context_type].append(element)
        
        # Assemble with clear hierarchy
        assembled = "# CONTEXT INFORMATION\n\n"
        
        # Order context types by cognitive utility
        type_order = [ContextType.FACTUAL, ContextType.PROCEDURAL, ContextType.EXPERIENTIAL, 
                     ContextType.RELATIONAL, ContextType.TEMPORAL, ContextType.CONTEXTUAL]
        
        for context_type in type_order:
            if context_type in grouped:
                assembled += f"## {context_type.value.upper()}\n"
                
                # Sort elements within type by relevance
                sorted_elements = sorted(grouped[context_type], key=lambda x: x.relevance_score, reverse=True)
                
                for element in sorted_elements:
                    if len(assembled.split()) + len(element.content.split()) > max_tokens:
                        break
                    assembled += f"- {element.content}\n"
                
                assembled += "\n"
        
        return assembled
    
    def _chronological_assembly(self, elements: List[ContextElement], max_tokens: int) -> str:
        """⏰ TEMPORAL STRUCTURE: Organize by time-based relevance"""
        
        # Sort by timestamp and temporal relevance
        temporal_sorted = sorted(elements, key=lambda x: (x.timestamp, x.relevance_score), reverse=True)
        
        assembled = "# CONTEXT TIMELINE\n\n"
        
        for element in temporal_sorted:
            if len(assembled.split()) + len(element.content.split()) > max_tokens:
                break
            
            assembled += f"**{element.context_type.value}**: {element.content}\n\n"
        
        return assembled
    
    def _importance_first_assembly(self, elements: List[ContextElement], max_tokens: int) -> str:
        """🎯 IMPORTANCE-BASED: Pure relevance score ordering"""
        
        importance_sorted = sorted(elements, key=lambda x: x.relevance_score, reverse=True)
        
        assembled = "# PRIORITY CONTEXT\n\n"
        
        for i, element in enumerate(importance_sorted, 1):
            if len(assembled.split()) + len(element.content.split()) > max_tokens:
                break
            
            assembled += f"{i}. **{element.context_type.value}** (Score: {element.relevance_score:.2f})\n"
            assembled += f"   {element.content}\n\n"
        
        return assembled
    
    def _cognitive_load_assembly(self, elements: List[ContextElement], max_tokens: int) -> str:
        """🧠 COGNITIVE OPTIMIZATION: Balance information density for optimal processing"""
        
        # Analyze cognitive complexity of each element
        complexity_scores = []
        for element in elements:
            complexity = self._calculate_cognitive_complexity(element.content)
            complexity_scores.append((element, complexity))
        
        # Arrange for optimal cognitive flow: simple → complex → simple
        complexity_scores.sort(key=lambda x: x[1])
        
        simple_elements = [x[0] for x in complexity_scores[:len(complexity_scores)//3]]
        complex_elements = [x[0] for x in complexity_scores[len(complexity_scores)//3:2*len(complexity_scores)//3]]
        moderate_elements = [x[0] for x in complexity_scores[2*len(complexity_scores)//3:]]
        
        assembled = "# OPTIMIZED CONTEXT FLOW\n\n"
        
        # Cognitive load pattern: Foundation → Complexity → Integration
        sections = [
            ("## FOUNDATION", simple_elements),
            ("## DETAILED ANALYSIS", complex_elements),
            ("## INTEGRATION", moderate_elements)
        ]
        
        for section_name, section_elements in sections:
            assembled += f"{section_name}\n"
            
            for element in section_elements:
                if len(assembled.split()) + len(element.content.split()) > max_tokens:
                    break
                assembled += f"- {element.content}\n"
            
            assembled += "\n"
        
        return assembled
    
    def _calculate_cognitive_complexity(self, content: str) -> float:
        """🧠 COMPLEXITY ANALYSIS: Estimate cognitive processing requirements"""
        
        # Simple heuristics for cognitive complexity
        factors = {
            'length': min(len(content) / 1000, 1.0),  # Longer = more complex
            'technical_terms': len([w for w in content.split() if len(w) > 10]) / len(content.split()),
            'sentence_complexity': content.count(',') + content.count(';') / len(content.split('.')),
            'abstract_concepts': len([w for w in content.lower().split() if w in ['concept', 'theory', 'principle', 'framework']]) / len(content.split())
        }
        
        return sum(factors.values()) / len(factors)
    
    def _calculate_quality_metrics(self, elements: List[ContextElement], assembled_context: str) -> Dict[str, float]:
        """📊 QUALITY ASSESSMENT: Comprehensive context quality evaluation"""
        
        return {
            'relevance_average': np.mean([e.relevance_score for e in elements]),
            'diversity_score': len(set(e.context_type for e in elements)) / len(ContextType),
            'coherence_score': self._assess_coherence(assembled_context),
            'completeness_score': self._assess_completeness(elements),
            'efficiency_score': self._assess_token_efficiency(elements, assembled_context)
        }
    
    def _assess_coherence(self, context: str) -> float:
        """🔗 COHERENCE ANALYSIS: Measure logical flow and connectivity"""
        
        # Simple coherence heuristics
        sentences = context.split('.')
        
        # Check for transition words and logical connectors
        transition_indicators = ['however', 'therefore', 'moreover', 'furthermore', 'additionally']
        transitions = sum(1 for sentence in sentences if any(indicator in sentence.lower() for indicator in transition_indicators))
        
        return min(transitions / max(len(sentences) - 1, 1), 1.0)
    
    def _assess_completeness(self, elements: List[ContextElement]) -> float:
        """✅ COMPLETENESS ANALYSIS: Measure information coverage"""
        
        # Check coverage of different context types
        types_covered = len(set(e.context_type for e in elements))
        total_types = len(ContextType)
        
        return types_covered / total_types
    
    def _assess_token_efficiency(self, elements: List[ContextElement], assembled_context: str) -> float:
        """⚡ EFFICIENCY ANALYSIS: Measure information density and token utilization"""
        
        total_content_tokens = sum(len(e.content.split()) for e in elements)
        assembled_tokens = len(assembled_context.split())
        
        # Efficiency = how much valuable content vs assembly overhead
        if assembled_tokens == 0:
            return 0.0
        
        return total_content_tokens / assembled_tokens
    
    def _generate_optimization_suggestions(self, 
                                         elements: List[ContextElement], 
                                         assembled_context: str,
                                         quality_metrics: Dict[str, float]) -> List[str]:
        """🔧 OPTIMIZATION RECOMMENDATIONS: Actionable improvements for context assembly"""
        
        suggestions = []
        
        if quality_metrics['relevance_average'] < 0.7:
            suggestions.append("Consider refining retrieval criteria to improve average relevance")
        
        if quality_metrics['diversity_score'] < 0.5:
            suggestions.append("Increase context type diversity for more comprehensive coverage")
        
        if quality_metrics['coherence_score'] < 0.6:
            suggestions.append("Improve context flow with better transition elements")
        
        if quality_metrics['completeness_score'] < 0.7:
            suggestions.append("Add more context types to improve information completeness")
        
        if quality_metrics['efficiency_score'] < 0.6:
            suggestions.append("Optimize assembly structure to reduce overhead and improve token efficiency")
        
        return suggestions

# 🎯 PHASE II: ACTIONABLE IMPLEMENTATION STRATEGIES

class SmartContextSystem:
    """
    🏗️ COMPLETE SYSTEM INTEGRATION: Production-ready intelligent context management
    
    STRATEGIC IMPLEMENTATION: This system demonstrates how to integrate all components
    into a cohesive, optimizable context engineering platform
    """
    
    def __init__(self, 
                 retrieval_strategy: ContextRelevanceStrategy = ContextRelevanceStrategy.HYBRID_SCORING,
                 assembly_strategy: str = 'cognitive_load_optimized'):
        
        # Initialize DSPy environment
        dspy.settings.configure(
            lm=dspy.OpenAI(model="gpt-4"),
            rm=dspy.ColBERTv2()
        )
        
        # Core system components
        self.retriever = IntelligentContextRetriever(retrieval_strategy)
        self.assembler = AdaptiveContextAssembler()
        
        # System configuration
        self.default_assembly_strategy = assembly_strategy
        self.performance_tracker = ContextPerformanceTracker()
        
        # Learning and optimization
        self.context_cache = {}
        self.optimization_history = []
    
    def generate_smart_context(self,
                              query: str,
                              task_type: str,
                              user_context: Dict[str, Any],
                              max_context_elements: int = 10,
                              max_tokens: int = 4000,
                              assembly_strategy: Optional[str] = None) -> ContextAssemblyResult:
        """
        🎯 PRIMARY INTERFACE: Complete intelligent context generation workflow
        
        STRATEGIC PROCESS:
        1. Intelligent retrieval with multi-dimensional scoring
        2. Strategic assembly based on cognitive optimization
        3. Quality assessment and optimization recommendations
        4. Performance tracking for continuous improvement
        """
        
        # Use provided strategy or system default
        strategy = assembly_strategy or self.default_assembly_strategy
        
        # Check cache for optimization
        cache_key = self._generate_cache_key(query, task_type, user_context)
        if cache_key in self.context_cache:
            cached_result = self.context_cache[cache_key]
            cached_result.processing_metadata['cache_hit'] = True
            return cached_result
        
        # Phase 1: Intelligent context retrieval
        context_elements = self.retriever(
            query=query,
            task_type=task_type,
            user_context=user_context,
            max_context_elements=max_context_elements
        )
        
        # Phase 2: Strategic context assembly
        assembly_result = self.assembler(
            context_elements=context_elements,
            task_type=task_type,
            assembly_strategy=strategy,
            max_tokens=max_tokens
        )
        
        # Phase 3: Performance tracking and optimization
        self.performance_tracker.record_context_usage(assembly_result, query, task_type)
        
        # Phase 4: Cache optimization
        if assembly_result.quality_metrics['relevance_average'] > 0.75:
            self.context_cache[cache_key] = assembly_result
        
        assembly_result.processing_metadata['cache_hit'] = False
        
        return assembly_result
    
    def optimize_system_performance(self, training_examples: List[Dict[str, Any]]):
        """
        ⚙️ SYSTEM OPTIMIZATION: Use DSPy optimizers to improve context generation
        
        OPTIMIZATION APPROACH:
        1. Analyze performance patterns from usage history
        2. Identify optimization opportunities
        3. Apply DSPy optimizers to improve component performance
        4. Validate improvements and update system parameters
        """
        
        print("🔄 Starting context system optimization...")
        
        # Convert training examples to DSPy format
        dspy_examples = []
        for example in training_examples:
            dspy_example = dspy.Example(
                query=example['query'],
                task_type=example['task_type'],
                user_context=example['user_context'],
                expected_quality=example.get('expected_quality', 0.8)
            )
            dspy_examples.append(dspy_example)
        
        # Define optimization metric
        def context_quality_metric(example, prediction, trace=None):
            """Metric to evaluate context quality improvement"""
            # This would be implemented based on actual quality assessments
            return prediction.quality_metrics.get('relevance_average', 0.0) >= example.expected_quality
        
        # Apply DSPy optimization
        optimizer = dspy.BootstrapFewShot(metric=context_quality_metric, max_examples=10)
        
        # Optimize retrieval component
        optimized_retriever = optimizer.compile(self.retriever, trainset=dspy_examples)
        
        # Optimize assembly component  
        optimized_assembler = optimizer.compile(self.assembler, trainset=dspy_examples)
        
        # Update system with optimized components
        self.retriever = optimized_retriever
        self.assembler = optimized_assembler
        
        print("✅ Context system optimization complete!")
        
        return {
            'optimization_applied': True,
            'components_optimized': ['retriever', 'assembler'],
            'training_examples_used': len(dspy_examples)
        }
    
    def get_system_analytics(self) -> Dict[str, Any]:
        """📊 SYSTEM ANALYTICS: Comprehensive performance and usage insights"""
        return self.performance_tracker.generate_analytics_report()
    
    def _generate_cache_key(self, query: str, task_type: str, user_context: Dict[str, Any]) -> str:
        """🔑 CACHE OPTIMIZATION: Generate efficient cache keys for context reuse"""
        key_components = [
            query[:100],  # Truncate for efficiency
            task_type,
            str(sorted(user_context.items()))
        ]
        
        return hashlib.md5('|'.join(key_components).encode()).hexdigest()

class ContextPerformanceTracker:
    """
    📈 PERFORMANCE MONITORING: Comprehensive tracking and analytics for context systems
    
    TRACKING DIMENSIONS:
    - Context quality metrics over time
    - User satisfaction and task completion
    - System efficiency and optimization opportunities
    - Component-level performance analysis
    """
    
    def __init__(self):
        self.usage_history = []
        self.performance_metrics = {
            'total_contexts_generated': 0,
            'average_quality_score': 0.0,
            'cache_hit_rate': 0.0,
            'optimization_cycles': 0
        }
    
    def record_context_usage(self, 
                           assembly_result: ContextAssemblyResult,
                           query: str,
                           task_type: str):
        """📝 USAGE RECORDING: Track context generation and performance"""
        
        usage_record = {
            'timestamp': time.time(),
            'query': query[:100],  # Truncate for privacy/efficiency
            'task_type': task_type,
            'quality_metrics': assembly_result.quality_metrics,
            'assembly_strategy': assembly_result.assembly_strategy,
            'context_elements_count': len(assembly_result.context_elements),
            'processing_metadata': assembly_result.processing_metadata
        }
        
        self.usage_history.append(usage_record)
        self._update_performance_metrics(usage_record)
    
    def _update_performance_metrics(self, usage_record: Dict[str, Any]):
        """📊 METRICS UPDATING: Maintain running performance statistics"""
        
        self.performance_metrics['total_contexts_generated'] += 1
        
        # Update average quality score
        current_avg = self.performance_metrics['average_quality_score']
        count = self.performance_metrics['total_contexts_generated']
        new_quality = usage_record['quality_metrics']['relevance_average']
        
        self.performance_metrics['average_quality_score'] = (
            (current_avg * (count - 1) + new_quality) / count
        )
        
        # Update cache hit rate
        cache_hits = sum(1 for record in self.usage_history 
                        if record['processing_metadata'].get('cache_hit', False))
        self.performance_metrics['cache_hit_rate'] = cache_hits / count
    
    def generate_analytics_report(self) -> Dict[str, Any]:
        """📋 ANALYTICS REPORT: Comprehensive system performance analysis"""
        
        if not self.usage_history:
            return {"status": "no_data", "message": "No usage data available"}
        
        # Quality trend analysis
        quality_trend = [record['quality_metrics']['relevance_average'] 
                        for record in self.usage_history[-10:]]
        
        # Task type performance analysis
        task_performance = {}
        for record in self.usage_history:
            task_type = record['task_type']
            if task_type not in task_performance:
                task_performance[task_type] = []
            task_performance[task_type].append(record['quality_metrics']['relevance_average'])
        
        # Generate optimization recommendations
        optimization_recommendations = self._generate_system_recommendations()
        
        return {
            'performance_summary': self.performance_metrics,
            'quality_trend': {
                'recent_scores': quality_trend,
                'trend_direction': 'improving' if len(quality_trend) > 1 and quality_trend[-1] > quality_trend[0] else 'stable'
            },
            'task_type_analysis': {
                task: {
                    'average_quality': np.mean(scores),
                    'consistency': 1 - np.std(scores),
                    'sample_count': len(scores)
                }
                for task, scores in task_performance.items()
            },
            'optimization_recommendations': optimization_recommendations,
            'system_health': self._assess_system_health()
        }
    
    def _generate_system_recommendations(self) -> List[str]:
        """🔧 OPTIMIZATION RECOMMENDATIONS: Data-driven system improvement suggestions"""
        
        recommendations = []
        
        if self.performance_metrics['average_quality_score'] < 0.7:
            recommendations.append("Consider optimizing retrieval strategies to improve context relevance")
        
        if self.performance_metrics['cache_hit_rate'] < 0.2:
            recommendations.append("Implement more aggressive caching strategies for frequently used contexts")
        
        if len(self.usage_history) > 100:
            recommendations.append("System ready for advanced DSPy optimization with collected usage data")
        
        return recommendations
    
    def _assess_system_health(self) -> str:
        """🏥 SYSTEM HEALTH: Overall system performance assessment"""
        
        quality_score = self.performance_metrics['average_quality_score']
        
        if quality_score > 0.8:
            return "excellent"
        elif quality_score > 0.7:
            return "good"
        elif quality_score > 0.6:
            return "fair"
        else:
            return "needs_improvement"

# 🎓 HANDS-ON LEARNING EXERCISES

def demonstrate_smart_context_system():
    """
    🛠️ PRACTICAL DEMONSTRATION: Complete smart context system in action
    """
    
    print("🎯 SMART CONTEXT SYSTEM DEMONSTRATION")
    print("=" * 50)
    
    # Initialize the system
    context_system = SmartContextSystem()
    
    # Test scenarios
    test_scenarios = [
        {
            "query": "How do I implement a secure authentication system for a web application?",
            "task_type": "technical_implementation",
            "user_context": {"expertise_level": "intermediate", "domain": "web_development"}
        },
        {
            "query": "Analyze the market trends for renewable energy investments",
            "task_type": "business_analysis",
            "user_context": {"expertise_level": "expert", "domain": "finance"}
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 SCENARIO {i}: {scenario['task_type']}")
        print(f"Query: {scenario['query'][:60]}...")
        
        # Generate smart context
        result = context_system.generate_smart_context(
            query=scenario["query"],
            task_type=scenario["task_type"],
            user_context=scenario["user_context"]
        )
        
        print(f"📊 Quality Metrics:")
        for metric, value in result.quality_metrics.items():
            print(f"  - {metric}: {value:.3f}")
        
        print(f"🔧 Optimization Suggestions:")
        for suggestion in result.optimization_suggestions[:2]:
            print(f"  - {suggestion}")
        
        print(f"📋 Context Elements Used: {len(result.context_elements)}")
        print(f"🎯 Assembly Strategy: {result.assembly_strategy}")
    
    # Show system analytics
    analytics = context_system.get_system_analytics()
    print(f"\n📈 SYSTEM ANALYTICS:")
    print(f"Contexts Generated: {analytics['performance_summary']['total_contexts_generated']}")
    print(f"Average Quality: {analytics['performance_summary']['average_quality_score']:.3f}")
    print(f"System Health: {analytics['system_health']}")

# 🏆 STUDENT CHALLENGES

class Module4Challenges:
    """🎯 PROGRESSIVE CHALLENGES: Master smart context systems through practice"""
    
    @staticmethod
    def challenge_1_context_scorer():
        """
        🟢 BEGINNER CHALLENGE: Build a custom context relevance scorer
        
        Requirements:
        1. Create a DSPy signature for context scoring
        2. Implement scoring based on multiple factors
        3. Test with different context types
        """
        # TODO: Student implementation
        pass
    
    @staticmethod
    def challenge_2_assembly_strategy():
        """
        🟡 INTERMEDIATE CHALLENGE: Design a custom assembly strategy
        
        Requirements:
        1. Create a new assembly strategy for specific use cases
        2. Implement cognitive load optimization
        3. Compare performance with existing strategies
        """
        # TODO: Student implementation
        pass
    
    @staticmethod
    def challenge_3_optimization_system():
        """
        🔴 ADVANCED CHALLENGE: Build a self-optimizing context system
        
        Requirements:
        1. Implement feedback loops for performance improvement
        2. Create adaptive strategies based on usage patterns
        3. Design comprehensive evaluation metrics
        """
        # TODO: Student implementation
        pass

if __name__ == "__main__":
    # Run the demonstration
    demonstrate_smart_context_system()