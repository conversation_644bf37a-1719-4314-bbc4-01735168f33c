# 🎯 INTELLIGENT DOCUMENT ANALYSIS SYSTEM
"""
🚀 LEARNING OBJECTIVE: Build a complete DSPy application that demonstrates:
- Real-world problem solving
- Systematic architecture design  
- Progressive complexity management
- Production-ready patterns

🔍 SYSTEM REQUIREMENTS ANALYSIS:
1. Accept various document types (PDF, text, markdown)
2. Extract key information intelligently
3. Provide structured analysis with confidence scores
4. Support multiple analysis types (summary, sentiment, key points)
5. Optimize performance automatically

📊 COMPLEXITY PROGRESSION:
Level 1: Basic document summarization
Level 2: Multi-faceted analysis with confidence
Level 3: Adaptive analysis based on document type
Level 4: Self-optimizing system with feedback loops
"""

import dspy
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import json

# 🏗️ PHASE I: SYSTEMATIC DECONSTRUCTION

class DocumentType(Enum):
    """🔍 ANALYTICAL INSIGHT: Different documents need different analysis approaches"""
    ACADEMIC_PAPER = "academic_paper"
    BUSINESS_REPORT = "business_report" 
    NEWS_ARTICLE = "news_article"
    LEGAL_DOCUMENT = "legal_document"
    GENERAL_TEXT = "general_text"

class AnalysisType(Enum):
    """📊 STRATEGIC BREAKDOWN: Multiple analysis dimensions for comprehensive understanding"""
    SUMMARY = "summary"
    KEY_POINTS = "key_points"
    SENTIMENT = "sentiment"
    ENTITIES = "entities"
    RECOMMENDATIONS = "recommendations"
    COMPREHENSIVE = "comprehensive"

@dataclass
class DocumentAnalysisResult:
    """📋 STRUCTURED OUTPUT: Clear, actionable results with quality metrics"""
    document_type: DocumentType
    analysis_type: AnalysisType
    content: Dict[str, Any]
    confidence_score: float
    processing_metadata: Dict[str, Any]
    recommendations: List[str]

# 🏛️ PILLAR 1: SOPHISTICATED SIGNATURES

class DocumentClassificationSignature(dspy.Signature):
    """
    🎯 CRITICAL DESIGN DECISION: Document type classification drives downstream analysis
    This signature identifies document characteristics to optimize processing approach
    """
    document_text = dspy.InputField(desc="Raw document content to classify")
    document_features = dspy.InputField(desc="Document metadata (length, structure, format)")
    
    document_type = dspy.OutputField(desc="Classified document type from predefined categories")
    confidence = dspy.OutputField(desc="Classification confidence score (0-1)")
    reasoning = dspy.OutputField(desc="Explanation for classification decision")

class AdaptiveAnalysisSignature(dspy.Signature):
    """
    🔧 SOPHISTICATED REASONING: Analysis adapts based on document type and user needs
    This demonstrates how signatures can encode complex domain knowledge
    """
    document_text = dspy.InputField(desc="Document content to analyze")
    document_type = dspy.InputField(desc="Previously classified document type")
    analysis_type = dspy.InputField(desc="Requested type of analysis")
    user_context = dspy.InputField(desc="User's specific needs and background")
    
    structured_analysis = dspy.OutputField(desc="Comprehensive analysis tailored to document type")
    key_insights = dspy.OutputField(desc="Most important findings and implications")
    confidence_assessment = dspy.OutputField(desc="Quality and reliability assessment")
    next_steps = dspy.OutputField(desc="Recommended follow-up actions")

class SpecializedSummarySignature(dspy.Signature):
    """
    📚 DOMAIN-SPECIFIC INTELLIGENCE: Specialized summaries based on document characteristics
    Different document types require different summarization strategies
    """
    document_text = dspy.InputField(desc="Full document content")
    document_type = dspy.InputField(desc="Document classification")
    target_audience = dspy.InputField(desc="Intended audience for the summary")
    summary_length = dspy.InputField(desc="Desired summary length (brief/moderate/detailed)")
    
    executive_summary = dspy.OutputField(desc="High-level overview for decision makers")
    technical_summary = dspy.OutputField(desc="Detailed technical content summary")
    action_items = dspy.OutputField(desc="Specific actionable items identified")
    quality_score = dspy.OutputField(desc="Summary quality and completeness score")

# 🏛️ PILLAR 2: INTELLIGENT MODULES

class DocumentTypeClassifier(dspy.Module):
    """
    🔍 ANALYTICAL COMPONENT: Intelligent document classification with reasoning
    Uses chain-of-thought to provide explainable classification decisions
    """
    
    def __init__(self):
        super().__init__()
        self.classifier = dspy.ChainOfThought(DocumentClassificationSignature)
        self.confidence_threshold = 0.7
    
    def forward(self, document_text: str, metadata: Dict[str, Any]) -> dspy.Prediction:
        """
        🎯 STRATEGIC PROCESSING: Multi-factor document classification
        Combines content analysis with structural metadata for robust classification
        """
        
        # Extract document features for classification
        features = self._extract_document_features(document_text, metadata)
        
        # Perform classification with reasoning
        classification_result = self.classifier(
            document_text=document_text[:2000],  # Truncate for efficiency
            document_features=str(features)
        )
        
        # Validate classification confidence
        if float(classification_result.confidence) < self.confidence_threshold:
            # Fallback to general classification with warning
            classification_result.document_type = DocumentType.GENERAL_TEXT.value
            classification_result.reasoning += " [LOW CONFIDENCE: Using general classification]"
        
        return classification_result
    
    def _extract_document_features(self, text: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """🔧 FEATURE ENGINEERING: Extract structural and content-based features"""
        return {
            "length": len(text),
            "paragraph_count": text.count('\n\n'),
            "has_citations": bool(text.count('[') > 3),
            "formal_language": any(word in text.lower() for word in ['hereby', 'whereas', 'therefore']),
            "technical_terms": len([w for w in text.split() if len(w) > 12]),
            "metadata": metadata
        }

class AdaptiveDocumentAnalyzer(dspy.Module):
    """
    🧠 INTELLIGENT REASONING ENGINE: Context-aware document analysis
    Adapts analysis strategy based on document type and user requirements
    """
    
    def __init__(self):
        super().__init__()
        self.analyzer = dspy.ChainOfThought(AdaptiveAnalysisSignature)
        self.summarizer = dspy.ChainOfThought(SpecializedSummarySignature)
        
        # 📊 PERFORMANCE OPTIMIZATION: Cache frequently used patterns
        self.analysis_cache = {}
    
    def forward(self, 
                document_text: str, 
                document_type: DocumentType,
                analysis_type: AnalysisType,
                user_context: str = "") -> DocumentAnalysisResult:
        """
        🎯 COMPREHENSIVE ANALYSIS PIPELINE: Multi-dimensional document understanding
        Combines classification insights with adaptive analysis strategies
        """
        
        # Generate cache key for optimization
        cache_key = hash((document_text[:100], document_type.value, analysis_type.value))
        
        if cache_key in self.analysis_cache:
            return self.analysis_cache[cache_key]
        
        # Perform adaptive analysis
        analysis_result = self.analyzer(
            document_text=document_text,
            document_type=document_type.value,
            analysis_type=analysis_type.value,
            user_context=user_context or "General business user"
        )
        
        # Generate specialized summary if requested
        summary_result = None
        if analysis_type in [AnalysisType.SUMMARY, AnalysisType.COMPREHENSIVE]:
            summary_result = self.summarizer(
                document_text=document_text,
                document_type=document_type.value,
                target_audience=user_context or "Business professionals",
                summary_length="moderate"
            )
        
        # Construct structured result
        result = DocumentAnalysisResult(
            document_type=document_type,
            analysis_type=analysis_type,
            content={
                "analysis": analysis_result.structured_analysis,
                "insights": analysis_result.key_insights,
                "summary": summary_result.executive_summary if summary_result else None,
                "technical_details": summary_result.technical_summary if summary_result else None,
                "action_items": summary_result.action_items if summary_result else []
            },
            confidence_score=float(analysis_result.confidence_assessment.split(':')[0] if ':' in str(analysis_result.confidence_assessment) else "0.8"),
            processing_metadata={
                "reasoning_steps": getattr(analysis_result, 'reasoning', []),
                "processing_time": "optimized",
                "cache_used": False
            },
            recommendations=analysis_result.next_steps.split('\n') if hasattr(analysis_result, 'next_steps') else []
        )
        
        # Cache result for performance
        self.analysis_cache[cache_key] = result
        
        return result

# 🏛️ PILLAR 3: SYSTEM ORCHESTRATION

class IntelligentDocumentSystem:
    """
    🏗️ COMPLETE SYSTEM ARCHITECTURE: Production-ready document analysis platform
    Demonstrates enterprise-level DSPy application design patterns
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        ⚙️ SYSTEM INITIALIZATION: Configurable, scalable architecture
        """
        
        # Configure DSPy environment
        default_config = {
            "lm": dspy.OpenAI(model="gpt-4"),
            "rm": dspy.ColBERTv2(),  # For future retrieval-augmented features
            "cache_enabled": True,
            "optimization_enabled": True
        }
        
        if config:
            default_config.update(config)
        
        dspy.settings.configure(
            lm=default_config["lm"],
            rm=default_config.get("rm")
        )
        
        # Initialize core components
        self.classifier = DocumentTypeClassifier()
        self.analyzer = AdaptiveDocumentAnalyzer()
        
        # System configuration
        self.config = default_config
        self.performance_metrics = {
            "documents_processed": 0,
            "average_confidence": 0.0,
            "error_rate": 0.0
        }
    
    def analyze_document(self, 
                        document_text: str,
                        analysis_type: Union[AnalysisType, str] = AnalysisType.COMPREHENSIVE,
                        user_context: str = "",
                        metadata: Optional[Dict[str, Any]] = None) -> DocumentAnalysisResult:
        """
        🎯 PRIMARY INTERFACE: Complete document analysis workflow
        This is the main entry point that coordinates all system components
        """
        
        try:
            # Phase 1: Document Classification
            classification = self.classifier(
                document_text=document_text,
                metadata=metadata or {}
            )
            
            document_type = DocumentType(classification.document_type)
            
            # Phase 2: Adaptive Analysis  
            if isinstance(analysis_type, str):
                analysis_type = AnalysisType(analysis_type)
            
            result = self.analyzer(
                document_text=document_text,
                document_type=document_type,
                analysis_type=analysis_type,
                user_context=user_context
            )
            
            # Phase 3: Performance Tracking
            self._update_performance_metrics(result)
            
            return result
            
        except Exception as e:
            # Robust error handling with fallback
            return self._handle_analysis_error(e, document_text, analysis_type)
    
    def batch_analyze(self, 
                     documents: List[Dict[str, Any]],
                     analysis_type: AnalysisType = AnalysisType.SUMMARY) -> List[DocumentAnalysisResult]:
        """
        📊 BATCH PROCESSING: Efficient analysis of multiple documents
        Demonstrates scalability and optimization patterns
        """
        
        results = []
        
        for doc_data in documents:
            try:
                result = self.analyze_document(
                    document_text=doc_data["text"],
                    analysis_type=analysis_type,
                    user_context=doc_data.get("context", ""),
                    metadata=doc_data.get("metadata", {})
                )
                results.append(result)
                
            except Exception as e:
                # Continue processing other documents even if one fails
                error_result = self._handle_analysis_error(e, doc_data["text"], analysis_type)
                results.append(error_result)
        
        return results
    
    def get_performance_report(self) -> Dict[str, Any]:
        """📈 SYSTEM MONITORING: Performance metrics and optimization insights"""
        return {
            "system_metrics": self.performance_metrics,
            "configuration": {k: str(v) for k, v in self.config.items()},
            "optimization_suggestions": self._generate_optimization_suggestions()
        }
    
    def _update_performance_metrics(self, result: DocumentAnalysisResult):
        """📊 INTERNAL MONITORING: Track system performance over time"""
        self.performance_metrics["documents_processed"] += 1
        
        # Update running average confidence
        current_avg = self.performance_metrics["average_confidence"]
        count = self.performance_metrics["documents_processed"]
        
        self.performance_metrics["average_confidence"] = (
            (current_avg * (count - 1) + result.confidence_score) / count
        )
    
    def _handle_analysis_error(self, error: Exception, document_text: str, analysis_type: AnalysisType) -> DocumentAnalysisResult:
        """🛡️ ERROR RECOVERY: Graceful degradation with informative feedback"""
        return DocumentAnalysisResult(
            document_type=DocumentType.GENERAL_TEXT,
            analysis_type=analysis_type,
            content={
                "error": str(error),
                "partial_analysis": document_text[:500] + "..." if len(document_text) > 500 else document_text,
                "status": "error_fallback_used"
            },
            confidence_score=0.1,
            processing_metadata={"error_occurred": True, "error_type": type(error).__name__},
            recommendations=["Review document format", "Check system configuration", "Retry with simpler analysis"]
        )
    
    def _generate_optimization_suggestions(self) -> List[str]:
        """🔧 CONTINUOUS IMPROVEMENT: System optimization recommendations"""
        suggestions = []
        
        if self.performance_metrics["average_confidence"] < 0.7:
            suggestions.append("Consider optimizing signatures for better confidence scores")
        
        if self.performance_metrics["documents_processed"] > 100:
            suggestions.append("Implement advanced caching strategies for frequently analyzed document types")
        
        suggestions.append("Consider enabling automatic DSPy optimization for production workloads")
        
        return suggestions

# 🎓 HANDS-ON LEARNING EXERCISES

def demonstrate_system_capabilities():
    """
    🛠️ PRACTICAL DEMONSTRATION: See the complete system in action
    This function shows all system capabilities with real examples
    """
    
    # Initialize the system
    doc_system = IntelligentDocumentSystem()
    
    # Sample documents for testing
    test_documents = [
        {
            "text": """
            EXECUTIVE SUMMARY
            
            Our Q3 financial results demonstrate strong growth across all business segments.
            Revenue increased 15% year-over-year to $2.4B, driven by robust demand in our
            core markets. Operating margins improved to 22%, reflecting operational efficiency
            gains and favorable market conditions.
            
            KEY HIGHLIGHTS:
            - Revenue: $2.4B (+15% YoY)
            - Operating Margin: 22% (+2pp YoY)  
            - Net Income: $485M (+18% YoY)
            - Free Cash Flow: $520M (+25% YoY)
            
            OUTLOOK:
            We remain optimistic about Q4 performance and are raising our full-year guidance.
            Market conditions remain favorable, and our strategic initiatives are delivering
            expected results. We anticipate continued growth momentum into 2024.
            """,
            "context": "Financial analyst reviewing quarterly results",
            "metadata": {"source": "Q3_earnings_report.pdf", "quarter": "Q3_2023"}
        }
    ]
    
    print("🔍 SYSTEM DEMONSTRATION")
    print("=" * 50)
    
    # Single document analysis
    result = doc_system.analyze_document(
        document_text=test_documents[0]["text"],
        analysis_type=AnalysisType.COMPREHENSIVE,
        user_context=test_documents[0]["context"],
        metadata=test_documents[0]["metadata"]
    )
    
    print(f"📄 Document Type: {result.document_type.value}")
    print(f"🎯 Confidence Score: {result.confidence_score:.2f}")
    print(f"📊 Key Insights: {result.content['insights']}")
    print(f"🎬 Action Items: {', '.join(result.recommendations[:3])}")
    
    # Performance metrics
    performance = doc_system.get_performance_report()
    print(f"\n📈 System Performance:")
    print(f"Documents Processed: {performance['system_metrics']['documents_processed']}")
    print(f"Average Confidence: {performance['system_metrics']['average_confidence']:.2f}")
    
    return result

# 🎯 STUDENT CHALLENGES

class StudentChallenge:
    """
    🏆 PROGRESSIVE LEARNING CHALLENGES: Hands-on skill development
    These challenges reinforce learning through practical application
    """
    
    @staticmethod
    def challenge_1_basic_signature():
        """
        🟢 BEGINNER CHALLENGE: Create a simple email analysis signature
        
        Requirements:
        1. Input: email text, sender info
        2. Output: tone, urgency level, key points
        3. Include helpful descriptions for each field
        """
        # TODO: Student implementation
        pass
    
    @staticmethod  
    def challenge_2_reasoning_module():
        """
        🟡 INTERMEDIATE CHALLENGE: Build a contract risk assessment module
        
        Requirements:
        1. Use ChainOfThought for complex reasoning
        2. Identify potential risks and opportunities
        3. Provide confidence scores and recommendations
        """
        # TODO: Student implementation
        pass
    
    @staticmethod
    def challenge_3_complete_system():
        """
        🔴 ADVANCED CHALLENGE: Design a news article fact-checking system
        
        Requirements:
        1. Classify article credibility
        2. Identify key claims to verify
        3. Suggest verification sources
        4. Handle multiple article types (news, opinion, analysis)
        """
        # TODO: Student implementation
        pass

# 🔍 COMPREHENSION VERIFICATION
def module_3_assessment():
    """
    📝 LEARNING ASSESSMENT: Verify understanding of complete system design
    
    This assessment checks understanding of:
    - System architecture principles
    - Component interaction patterns  
    - Error handling and optimization
    - Real-world application design
    """
    
    assessment_questions = [
        {
            "concept": "System Architecture",
            "question": "How do the three DSPy pillars work together in the document system?",
            "learning_objective": "Understanding component interaction",
            "difficulty": "intermediate"
        },
        {
            "concept": "Error Handling", 
            "question": "What happens when document classification fails?",
            "learning_objective": "Robust system design principles",
            "difficulty": "advanced"
        },
        {
            "concept": "Optimization",
            "question": "How would you improve system performance for high-volume processing?",
            "learning_objective": "Performance optimization strategies",
            "difficulty": "expert"
        }
    ]
    
    return assessment_questions

if __name__ == "__main__":
    # Run the demonstration
    demonstrate_system_capabilities()